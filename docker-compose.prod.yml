version: '3.8'

services:
  web:
    container_name: golrang_web
    restart: unless-stopped
    build:
      context: .
      dockerfile: Dockerfile.prod
    command: gunicorn config.wsgi:application --bind 0.0.0.0:8000 --workers=32 --timeout 560
    volumes:
      - static_volume:/usr/src/app/static
    ports:
      - "8022:8000"
    env_file:
      - .env.prod
    depends_on:
      - postgres
    links:
      - postgres
    networks:
      - golrang

  postgres:
    container_name: golrang_db
    ports:
      - "5581:5432"
    restart: unless-stopped
    image: postgres:14.0
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    env_file:
      - .env.prod
    networks:
      - golrang
  golrang_redis:
    container_name: golrang_redis
    image: redis:alpine
    env_file: .env.prod
    volumes:
      - redis_data:/data
    networks:
      - golrang

  golrang_celery:
    container_name: golrang_celery
    build:
      context: .
      dockerfile: Dockerfile.prod
    env_file: .env.prod
    command: celery -A config worker -l info
    volumes:
      - .:/usr/src/app/
      - static_volume:/usr/src/app/static

    depends_on:
      - golrang_redis
    networks:
      - golrang


  # golrang_celery-beat:
  #   container_name: golrang_celery_beat
  #   build:
  #     context: .
  #     dockerfile: Dockerfile.prod
  #   env_file: .env.prod
  #   command: celery -A config beat -l info
  #   volumes:
  #     - .:/usr/src/app/
  #   depends_on:
  #     - golrang_redis
  #   networks:
  #     - golrang



volumes:
  postgres_data:
  static_volume:
  redis_data:

networks:
  golrang:
    driver: bridge
