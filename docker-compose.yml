version: '3.8'

services:
  web:
    container_name: golrang_web
    restart: unless-stopped
    build:
      context: .
      dockerfile: Dockerfile.prod
    command: python manage.py runserver 0.0.0.0:8000
    volumes:
      - static_volume:/usr/src/app/static
    ports:
      - "8022:8000"
    env_file:
      - .env.prod
    depends_on:
      - postgres
    links:
      - postgres
    networks:
      - golrang

  postgres:
    container_name: golrang_db
    ports:
      - "5581:5432"
    restart: unless-stopped
    image: postgres:14.0
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    env_file:
      - .env.prod
    networks:
      - golrang
  golrang_redis:
    container_name: golrang_redis
    image: redis:alpine
    env_file: .env.prod
    volumes:
      - redis_data:/data
    networks:
      - golrang



volumes:
  postgres_data:
  static_volume:
  redis_data:

networks:
  golrang:
    driver: bridge
