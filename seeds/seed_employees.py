import pandas as pd

# تعریف داده‌های نمونه به صورت دستی
# data = {
#     'national_code': [
#         '0011223344', '0011223355', '0011223366', '0011223377', '0011223388',
#         '0011223399', '0011223400', '0011223411', '0011223422', '0011223433',
#         '0011223444', '0011223455', '0011223466', '0011223477', '0011223488',
#         '0011223499', '0011223500', '0011223511', '0011223522', '0011223533'
#     ],
#     'fullname': [
#         'علی احمدی', 'محسن رضایی', 'مصطفی محمدی', 'سعید حسینی', 'حسین طاهری',
#         'رضا عباسی', 'امیرحسین خواجه‌پور', 'مصطفی ابراهیمی', 'احمد رحیمی', 'مصط<PERSON>ی علیزاده',
#         'سجاد موسوی', 'بهزاد کاظمی', 'مصطفی اسماعیلی', 'مصطفی حسینی', 'مصطفی رحمانی',
#         'مصطفی علیپور', 'مصطفی علیمحمدی', 'مصطفی علیجانی', 'مصطفی علیخانی', 'مصطفی علیشاهی'
#     ],
#     'company': [
#         'شرکت توسعه نرم‌افزار ایران', 'شرکت توسعه نرم‌افزار ایران', 'شرکت توسعه نرم‌افزار ایران',
#         'شرکت هوش مصنوعی ایران', 'شرکت هوش مصنوعی ایران', 'شرکت هوش مصنوعی ایران',
#         'شرکت امنیت اطلاعات ایران', 'شرکت امنیت اطلاعات ایران', 'شرکت امنیت اطلاعات ایران',
#         'شرکت توسعه نرم‌افزار ایران', 'شرکت توسعه نرم‌افزار ایران', 'شرکت توسعه نرم‌افزار ایران',
#         'شرکت هوش مصنوعی ایران', 'شرکت هوش مصنوعی ایران', 'شرکت هوش مصنوعی ایران',
#         'شرکت امنیت اطلاعات ایران', 'شرکت امنیت اطلاعات ایران', 'شرکت امنیت اطلاعات ایران',
#         'شرکت توسعه نرم‌افزار ایران', 'شرکت هوش مصنوعی ایران'
#     ],
#     'position': [
#         'توسعه‌دهنده وب (Frontend)', 'توسعه‌دهنده وب (Backend)', 'مهندس داده', 'برنامه‌نویس هوش مصنوعی',
#         'مهندس یادگیری ماشین', 'متخصص پردازش تصویر', 'مهندس امنیت شبکه', 'متخصص امنیت سیستم',
#         'برنامه‌نویس امنیت اطلاعات', 'توسعه‌دهنده اپلیکیشن موبایل', 'برنامه‌نویس بازی', 'مهندس سخت‌افزار امنیت',
#         'برنامه‌نویس هوش مصنوعی', 'مهندس داده', 'برنامه‌نویس یادگیری عمیق', 'مهندس امنیت ابری',
#         'برنامه‌نویس امنیت وب', 'برنامه‌نویس امنیت دیتابیس', 'برنامه‌نویس DevOps', 'مهندس رباتیک نرم‌افزاری'
#     ]
# }

data = [
    {
        "fullname": "محمد امین قربانی",
        "national_code": "*********",
        "company": "داده پرداز افق نو",
        "position": "مدیر محصول",
        "phone_number": "09922161241"
    },
    {
        "fullname": "فاطمه زهرا ابوطالبی",
        "national_code": "*********",
        "company": "داده پرداز افق نو",
        "position": "تست و کیفیت اپلیکیشن",
        "phone_number": "09922161241"
    },
    {
        "fullname": "علی کوپل پور",
        "national_code": "*********",
        "company": "داده پرداز افق نو",
        "position": "مدیر منابع انسانی",
        "phone_number": "09050058162"

    },    
    {
        "fullname": "سینا سجادی",
        "national_code": "*********",
        "company": "داده پرداز افق نو",
        "position": "توسعه دهنده فرانت اند",
        "phone_number": "09055808205"
    },        
    {
        "fullname": "علی رضا مرتضایی",
        "national_code": "*********",
        "company": "داده پرداز افق نو",
        "position": "توسعه دهنده بک اند",
        "phone_number": "09013077990"
    },            
    {
        "fullname": "کمیل نادری",
        "national_code": "*********",
        "company": "داده پرداز افق نو",
        "position": "توسعه دهنده فلاتر",
        "phone_number": "09922161241"
    },                
    {
        "fullname": "سینا حجازی",
        "national_code": "*********",
        "company": "داده پرداز افق نو",
        "position": "توسعه دهنده فلاتر",
        "phone_number": "09922161241"
    },                    
]


# تبدیل داده‌ها به DataFrame
df = pd.DataFrame(data)

# ذخیره DataFrame یونیک شده در فایل اکسل
df.to_excel('employees.xlsx', index=False, encoding='utf-8')

print("فایل اکسل یونیک با موفقیت ایجاد شد.")
