import pandas as pd

# داده‌ها
data = [
    {
        "fullname": "محمد امین قربانی",
        "national_code": "*********",
        "company": "داده پرداز افق نو",
        "position": "مدیر محصول",
        "phone_number": "09922161241"
    },
    {
        "fullname": "فاطمه زهرا ابوطالبی",
        "national_code": "*********",
        "company": "داده پرداز افق نو",
        "position": "تست و کیفیت اپلیکیشن",
        "phone_number": "09922161241"
    },
    {
        "fullname": "علی کوپل پور",
        "national_code": "*********",
        "company": "داده پرداز افق نو",
        "position": "مدیر منابع انسانی",
        "phone_number": "09050058162"
    },    
    {
        "fullname": "سینا سجادی",
        "national_code": "*********",
        "company": "داده پرداز افق نو",
        "position": "توسعه دهنده فرانت اند",
        "phone_number": "09055808205"
    },        
    {
        "fullname": "علی رضا مرتضایی",
        "national_code": "*********",
        "company": "داده پرداز افق نو",
        "position": "توسعه دهنده بک اند",
        "phone_number": "09013077990"
    },            
    {
        "fullname": "کمیل نادری",
        "national_code": "*********",
        "company": "داده پرداز افق نو",
        "position": "توسعه دهنده فلاتر",
        "phone_number": "09922161241"
    },                
    {
        "fullname": "سینا حجازی",
        "national_code": "*********",
        "company": "داده پرداز افق نو",
        "position": "توسعه دهنده فلاتر",
        "phone_number": "09922161241"
    },                    
]

# لیست برای ذخیره داده‌های جدید
evaluation_data = []

# حلقه برای ایجاد داده‌های ارزیابی
for evaluator in data:
    for evaluatee in data:
        # اگر کد ملی ارزیاب و ارزیابی شونده یکسان نباشد
        if evaluator["national_code"] != evaluatee["national_code"]:
            evaluation_data.append({
                "evaluator_national_code": evaluator["national_code"],
                "evaluator_fullname": evaluator["fullname"],
                "evaluatee_national_code": evaluatee["national_code"],
                "evaluatee_fullname": evaluatee["fullname"]
            })

# تبدیل داده‌ها به DataFrame
df_evaluation = pd.DataFrame(evaluation_data)

# ذخیره DataFrame به عنوان فایل اکسل
df_evaluation.to_excel("assignments.xlsx", index=False)

print("فایل اکسل ارزیابی با موفقیت ایجاد شد.")