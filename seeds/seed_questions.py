import pandas as pd

# تعریف داده‌های نمونه به صورت دستی
data = {
    'question': [
        'کارمند به طور کلی عملکرد خوبی دارد.',
        'کارمند در زمان تعیین‌شده وظایف خود را انجام می‌دهد.',
        'کارمند در برابر چالش‌ها رفتار مناسبی دارد.',
        'کارمند با همکاران خود رابطه خوبی دارد.',
        'کارمند بهبود مداوم در کار خود را دنبال می‌کند.',
        'کارمند مهارت‌های لازم برای انجام وظایف را دارد.',
        'کارمند به نحوه مدیریت تیم رضایت دارد.',
        'کارمند از محیط کار رضایت دارد.',
        'کارمند از فرصت‌های تحصیلی و آموزشی استفاده می‌کند.',
        'کارمند به راهنمایی و پشتیبانی مدیر رضایت دارد.',
        'نظر شما درباره ایده‌های نوآورانه کارمند چیست؟',
        'چه چالش‌هایی در رابطه با انجام وظایف وجود دارد؟',
        'نکاتی که می‌توانید درباره رفتار کارمند ذکر کنید؟',
        'چگونه می‌توانید عملکرد کارمند را بهبود ببخشید؟',
        'نظر شما درباره مدیریت زمان کارمند چیست؟'
    ],
    'category': [
        'عملکرد کارمند', 'مدیریت وقت', 'مدیریت چالش‌ها', 'رابطه با همکاران', 'بهبود مداوم',
        'مهارت‌های تخصصی', 'رضایت از مدیریت', 'محیط کار', 'آموزش و توسعه', 'پشتیبانی مدیریت',
        'نوآوری', 'چالش‌ها', 'رفتار', 'بهبود عملکرد', 'مدیریت زمان'
    ],
    'type': [
        'rating', 'rating', 'rating', 'rating', 'rating',
        'rating', 'rating', 'rating', 'rating', 'rating',
        'comment', 'comment', 'comment', 'comment', 'comment'
    ]
}

# تبدیل داده‌ها به DataFrame
df = pd.DataFrame(data)

# ذخیره DataFrame در فایل اکسل
df.to_excel('questions.xlsx', index=False, encoding='utf-8')

print("فایل اکسل سوالات با موفقیت ایجاد شد.")

<<<<<<< Updated upstream:seeds/seed_questions.py
=======
print("فایل اکسل سوالات با موفقیت ایجاد شد.")

>>>>>>> Stashed changes:seed_questions.py
