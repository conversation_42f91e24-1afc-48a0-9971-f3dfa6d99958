# pull official base image
FROM python:3.9

# set work directory
WORKDIR /usr/src/app

# set environment variables
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

RUN apt-get update
# RUN apt-get install -y vim
# RUN apt-get install -y ffmpeg
# RUN apt-get install -y cron
# install dependencies
RUN pip install --upgrade pip

COPY ./requirements.txt .
COPY .env.dev .env

RUN --mount=type=cache,target=/root/.cache  pip install -r requirements.txt

# copy entrypoint.sh
COPY ./entrypoint.sh .
RUN sed -i 's/\r$//g' /usr/src/app/entrypoint.sh
RUN chmod +x /usr/src/app/entrypoint.sh

# copy project
COPY . .

# run entrypoint.sh
# ENTRYPOINT ["/usr/src/app/entrypoint.sh"]
