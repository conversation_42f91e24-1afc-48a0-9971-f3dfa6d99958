# راهنمای ایجاد فایل‌های Excel تستی

این راهنما نحوه استفاده از دستورات Django management برای ایجاد فایل‌های Excel تستی را توضیح می‌دهد.

## پیش‌نیازها

ابتدا مطمئن شوید که بسته‌های مورد نیاز نصب شده‌اند:

```bash
pip install faker pandas openpyxl
```

## دستورات موجود

### 1. ایجاد فایل Excel کارمندان

```bash
# ایجاد فایل Excel با 1000 کارمند
python manage.py seed_employees --count 1000

# ایجاد فایل Excel با نام مخصوص
python manage.py seed_employees --count 500 --output employees_500.xlsx

# ایجاد فایل Excel با 100 کارمند (تست سریع)
python manage.py seed_employees --count 100 --output test_employees_small.xlsx
```

### 2. ایجاد فایل Excel Poll Assignments

```bash
# ایجاد فایل Excel با 1000 assignment
python manage.py seed_poll_assignments --count 1000

# ایجاد فایل Excel با تنظیمات سفارشی
python manage.py seed_poll_assignments \
  --count 2000 \
  --output custom_assignments.xlsx \
  --evaluators 300 \
  --evaluatees 800

# ایجاد فایل Excel کوچک برای تست
python manage.py seed_poll_assignments \
  --count 100 \
  --evaluators 20 \
  --evaluatees 50 \
  --output small_test.xlsx
```

### 3. ایجاد فایل Excel تست Import

```bash
# ایجاد فایل Excel با 1000 رکورد برای تست import
python manage.py generate_test_excel --count 1000

# ایجاد فایل Excel با نام مخصوص و تنظیمات سفارشی
python manage.py generate_test_excel \
  --count 2000 \
  --output import_test_2000.xlsx \
  --evaluators 250 \
  --evaluatees 600

# ایجاد فایل Excel کوچک برای تست سریع
python manage.py generate_test_excel \
  --count 50 \
  --output quick_test.xlsx \
  --evaluators 10 \
  --evaluatees 25
```

### 4. ایجاد تمام فایل‌های Excel (دستور جامع)

```bash
# ایجاد کامل فایل‌های Excel تستی
python manage.py seed_all_test_data \
  --employees 1000 \
  --assignments 1000

# ایجاد فایل‌های Excel با تنظیمات سفارشی
python manage.py seed_all_test_data \
  --employees 1500 \
  --assignments 2000 \
  --evaluators 300 \
  --evaluatees 700 \
  --output-dir my_test_files

# ایجاد فایل‌های Excel کوچک برای تست سریع
python manage.py seed_all_test_data \
  --employees 200 \
  --assignments 300 \
  --evaluators 50 \
  --evaluatees 100 \
  --output-dir quick_test
```

## مراحل توصیه شده برای تست

### مرحله 1: ایجاد فایل‌های Excel
```bash
# ایجاد کامل فایل‌های Excel تستی در دایرکتوری test_data
python manage.py seed_all_test_data \
  --employees 1000 \
  --assignments 2000 \
  --evaluators 200 \
  --evaluatees 500 \
  --output-dir test_data
```

**نتیجه:** سه فایل Excel ایجاد می‌شود:
- `test_data/employees_1000.xlsx` - فایل کارمندان
- `test_data/poll_assignments_2000.xlsx` - فایل assignments
- `test_data/import_test_2000.xlsx` - فایل آماده برای import

### مرحله 2: تست Import با Celery
```bash
# حالا می‌توانید فایل‌های Excel را در Admin Panel آپلود کنید:
# 1. برای کارمندان: Admin > Employee > Import
# 2. برای Poll Assignments: Admin > Evaluation > Poll Assignments > Import
```

### مرحله 3: بررسی فایل‌های ایجاد شده
```bash
# نمایش محتویات دایرکتوری test_data
ls -la test_data/

# نمایش اطلاعات فایل‌ها
file test_data/*.xlsx
```

## ویژگی‌های فایل‌های Excel ایجاد شده

### 1. فایل کارمندان
- ستون‌ها: fullname, email, national_code, phone_number, company, position, address, is_evaluator, is_active
- داده‌های فارسی واقع‌گرایانه
- کدهای ملی و ایمیل‌های یکتا

### 2. فایل Poll Assignments
- ستون‌ها: evaluator_national_code, evaluator_fullname, evaluatee_national_code, evaluatee_fullname
- مناسب برای import مستقیم در سیستم
- تضمین عدم تکرار evaluator و evaluatee در هر رکورد

### 3. فایل Import Test
- ساختار کاملاً مشابه با فایل Poll Assignments
- بهینه‌سازی شده برای تست عملیات import
- داده‌های متنوع و واقعی

## نکات مهم

### 1. مدیریت فایل‌ها
- تمام فایل‌ها در فرمت xlsx ایجاد می‌شوند
- فایل‌ها در دایرکتوری مشخص شده ذخیره می‌شوند
- نام فایل‌ها شامل تعداد رکوردها است

### 2. تنوع داده‌ها
- استفاده از Faker فارسی برای نام‌ها
- کدهای ملی یکتا و معتبر
- شرکت‌ها و سمت‌های متنوع ایرانی

### 3. عملکرد
- تولید سریع فایل‌های بزرگ
- استفاده بهینه از حافظه
- پیشرفت visible در console

### 4. سازگاری
- فایل‌های Excel قابل باز کردن در تمام برنامه‌ها
- ساختار مناسب برای import در Django Admin
- رمزگذاری UTF-8 برای متن‌های فارسی

## مثال‌های کاربردی

### تست حجم بالا (2000+ رکورد)
```bash
python manage.py generate_test_excel \
  --count 2000 \
  --output large_test.xlsx \
  --evaluators 300 \
  --evaluatees 800
```

### تست سریع (کم حجم)
```bash
python manage.py generate_test_excel \
  --count 50 \
  --output quick_test.xlsx \
  --evaluators 10 \
  --evaluatees 20
```

### ایجاد مجموعه کامل برای پروژه
```bash
python manage.py seed_all_test_data \
  --employees 1500 \
  --assignments 3000 \
  --evaluators 400 \
  --evaluatees 900 \
  --output-dir production_test_data
```

## تست Celery Task

پس از ایجاد فایل‌ها:

1. **آماده‌سازی:** ابتدا یک Poll در دیتابیس ایجاد کنید
2. **آپلود:** فایل Excel را از طریق Admin Panel آپلود کنید
3. **مشاهده:** فرایند Import در background اجرا می‌شود
4. **بررسی:** وضعیت پردازش را در Import Tasks مشاهده کنید

🎯 **هدف:** تست کردن عملکرد سیستم import بدون timeout با فایل‌های بزرگ

📁 **محل فایل‌ها:** تمام فایل‌های Excel در دایرکتوری `test_data/` یا دایرکتوری مشخص شده ذخیره می‌شوند.