import random
# import pyarabic.araby as araby
# from fuzzywuzzy import fuzz
# from utils.similarity import find_similarity ,rm_sign
import json
import os


os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
from django.core.wsgi import get_wsgi_application

application = get_wsgi_application()
from apps.employee.models import Employee
from apps.evaluation.models import Poll, Question, Response, PollAssignment, SelectableEmployee

# print(f'--PollAssignment->{Employee.objects.all()} ')
Employee.objects.all().delete()

# حذف تمامی Responseها
Response.objects.all().delete()

# حذف تمامی SelectableEmployeeها
SelectableEmployee.objects.all().delete()

# حذف تمامی PollAssignmentها
PollAssignment.objects.all().delete()

# حذف تمامی Questionها
Question.objects.all().delete()

# حذف تمامی Pollها
# Poll.objects.all().delete()