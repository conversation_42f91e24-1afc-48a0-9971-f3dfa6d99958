---
description: Repository Information Overview
alwaysApply: true
---

# Golrang Django Project Information

## Summary
A Django-based web application for employee evaluation and management. The project uses a PostgreSQL database, Redis for caching and Celery for task management, and is containerized with Docker.

## Structure
- **apps/**: Contains the main application modules (account, employee, evaluation, api)
- **config/**: Project configuration and settings
- **dynamic_preferences/**: Custom Django app for user preferences
- **static/**: Static files (CSS, JS, media)
- **templates/**: HTML templates
- **utils/**: Utility functions and helpers

## Language & Runtime
**Language**: Python
**Version**: 3.9 (based on Dockerfile)
**Framework**: Django
**Build System**: pip
**Package Manager**: pip

## Dependencies
**Main Dependencies**:
- Django (web framework)
- djangorestframework (API framework)
- Celery (task queue)
- <PERSON>is (caching and message broker)
- PostgreSQL (database)
- django-filer (file management)
- pandas (data processing)
- drf-yasg (API documentation)

**Development Dependencies**:
- django-debug-toolbar
- django-cors-headers

## Build & Installation
```bash
# Create and activate virtual environment
python -m venv .venv
source .venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Run migrations
python manage.py migrate

# Start development server
python manage.py runserver
```

## Docker
**Dockerfile**: Dockerfile, Dockerfile.prod
**Image**: Python 3.9
**Configuration**: Multi-container setup with web, PostgreSQL, and Redis services
**Run Command**:
```bash
docker-compose up -d
```

## Main Entry Points
**Web Application**: manage.py (Django management script)
**API Endpoints**: apps/api/urls.py
**Admin Interface**: Customized with limitless_dashboard

## Testing
No specific testing framework configuration found in the repository.

## Deployment
**Production Setup**:
```bash
# Build and run production containers
docker-compose -f docker-compose.prod.yml up -d
```

## Background Tasks
**Task Queue**: Celery
**Message Broker**: Redis
**Configuration**: config/celery.py
**Worker Start**:
```bash
celery -A config worker -l info
```