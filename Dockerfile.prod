# استفاده از ایمیج پایه سبک اما کاملتر (Debian-based)
FROM python:3.9-slim

# تنظیم دایرکتوری کاری
WORKDIR /usr/src/app

# تنظیم متغیرهای محیطی
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

# نصب وابستگیهای سیستمی
RUN apt-get update \
    && apt-get install -y \
        git \
        gcc \
        libpq-dev \
        python3-dev \
        libjpeg-dev \
        zlib1g-dev \
        freetype* 
    # && rm -rf /var/lib/apt/lists/*

# ارتقای pip و نصب numpy متناسب با pandas 1.3.4
RUN pip install --upgrade pip \
    && pip install numpy==1.23.5

# کپی فایلهای مورد نیاز
COPY ./requirements.txt .
COPY .env.prod .env

# نصب وابستگیهای پایتون با کش
RUN --mount=type=cache,target=/root/.cache \
    pip install -r requirements.txt 

RUN pip install psycopg2-binary
RUN pip install pandas==1.5.3


# اصلاح خطوط پایان فایل entrypoint
COPY ./entrypoint.sh .
RUN apt-get update && apt-get install -y dos2unix \
    && dos2unix /usr/src/app/entrypoint.sh \
    && chmod +x /usr/src/app/entrypoint.sh

# کپی پروژه
COPY . .

# اجرای entrypoint
ENTRYPOINT ["/usr/src/app/entrypoint.sh"]