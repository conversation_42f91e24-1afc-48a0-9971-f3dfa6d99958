"""
URL configuration for backend project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.0/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.conf.urls.i18n import i18n_patterns
from utils import UploadTmpMedia
from django.conf.urls import url
from django.http import JsonResponse
from django.shortcuts import render
from django.views.decorators.csrf import csrf_exempt
from rest_framework.decorators import api_view
from rest_framework.response import Response

from utils import absolute_url



api_patterns = [
    path('test/', include('apps.api.urls')),

    path('account/', include('apps.account.urls')),    
    path('', include('apps.evaluation.urls')),    

]


urlpatterns = [
    # path('admin/', admin.site.urls),
    path('api/', include(api_patterns)),
    # path('test/', include('apps.api.urls'))
]
urlpatterns += i18n_patterns(
    path('', include('limitless_dashboard.urls')),

)
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
