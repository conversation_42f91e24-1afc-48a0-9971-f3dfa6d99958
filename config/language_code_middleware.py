from django.http import HttpResponse
from apps.account.models import User
from django.shortcuts import redirect

ALLOWED_URLS = [
    "/login", "/admin", "telegram-sentry", 'bot-runner', "auth/google/", "/elalhabib/submit/", '/pay', 'paypal',
    'robots.txt', "/.well-known/", "about", "/download", 'dont-kill/'
]



def language_middleware(get_response):
    def middleware(request):
        # تنظیم زبان بر اساس پارامتر `language_code` در URL
        language_code = request.GET.get('language_code')
        if language_code:
            translation.activate(language_code)
            request.LANGUAGE_CODE = language_code
        
        # اگر کاربر در حال تلاش برای خروج است و مسیر خروج قدیمی است، به مسیر جدید هدایت شود
        if request.path == '/admin/logout/':
            return redirect(f'/{request.LANGUAGE_CODE}/admin/logout/')
        
        response = get_response(request)
        return response

    return middleware