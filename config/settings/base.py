"""
Django settings for backend project.

Generated by 'django-admin startproject' using Django 5.0.4.

For more information on this file, see
https://docs.djangoproject.com/en/5.0/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.0/ref/settings/
"""
import os
from pathlib import Path

import environ
from django.utils.translation import gettext_lazy as _


env = environ.Env(
    # set casting, default value
    # DEBUG=(bool, False)
)
# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent.parent

environ.Env.read_env(os.path.join(BASE_DIR, '.env'))

ALLOWED_HOSTS = env('DJANGO_ALLOWED_HOSTS').split(',')


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.0/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-7=3it+m^28^+0c1*9-*c*6g3ej63sz(97rq1^mp=!6e(mhmysh'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

X_FRAME_OPTIONS = 'SAMEORIGIN'

LOCAL_APPS = [
    "apps.account.apps.AccountConfig",
    "apps.employee.apps.EmployeeConfig",
    "apps.evaluation.apps.EvaluationConfig",
]

THIRD_PARTY_APPS = [
    'rest_framework',
    'rest_framework.authtoken',
    'drf_yasg',
    'easy_thumbnails',
    'dj_language',
    'dj_filer',
    'ajaxdatatable',

    'corsheaders',
    'django_filters',
    
]
INSTALLED_APPS = [
    'limitless_dashboard.apps.DashboardConfig',
    # 'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    *THIRD_PARTY_APPS,
    *LOCAL_APPS,
    
]

AUTHENTICATION_BACKENDS = [
    'django.contrib.auth.backends.ModelBackend',  # این خط را نگه دارید تا احراز هویت پیش‌فرض کار کند
]




OTP_SERIVCE_KEY = "33213d78f1234e99b81f94eefda77e45"


PHONENUMBER_DB_FORMAT = 'INTERNATIONAL'
PHONENUMBER_DEFAULT_FORMAT = 'INTERNATIONAL'

AUTH_USER_MODEL = "account.User"

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.locale.LocaleMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'config.language_code_middleware.language_middleware',
    'config.test_auth_middleware.test_auth_middleware',
]

ROOT_URLCONF = 'config.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [
            BASE_DIR / 'templates',
        ],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'django.template.context_processors.i18n',

            ],
        },
    },
]

WSGI_APPLICATION = 'config.wsgi.application'

REDIS_URL = env("REDIS_URL")

# django google recaptcha default keys
RECAPTCHA_PUBLIC_KEY = env('captcha_public_key')
RECAPTCHA_PRIVATE_KEY = env('captcha_private_key')

# custom settings
APPS_REORDER = {
    'auth': {
        'icon': 'icon-shield-check',
        'name': 'Authentication'
    },
    'account': {
        # 'icon': 'icon-',
        'name': 'account'

    }
}
# Database
# https://docs.djangoproject.com/en/5.0/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': env('POSTGRES_DB'),
        'USER': env('POSTGRES_USER'),
        'PASSWORD': env('POSTGRES_PASSWORD'),
        'HOST': env('POSTGRES_HOST'),
        'PORT': env('POSTGRES_PORT'),
        'ATOMIC_REQUESTS': True,
    },
}


CORS_ALLOW_ALL_ORIGINS = True

THUMBNAIL_ALIASES = {
    '': {
        'icon': {'size': (50, 50), 'crop': True},
        'large': {'size': (1200, 620), 'crop': False},
        'medium': {'size': (545, 545), 'crop': False},
        'small': {'size': (150, 150), 'crop': False},
    },
}

LANGUAGES_MAP = {
    'az': ['az', 'tr', 'fa', 'ar'],
    'tr': ['tr', 'az', 'fa', 'ar'],
    'ru': ['ru', 'az', 'tr', 'fa', 'ar'],
    'ar': ['ar', 'fa'],
    'ur': ['ur', 'en', 'fa', 'ar'],
    'en': ['en', 'ur', 'fa', 'ar'],
    'de': ['de', 'en', 'fr', 'es', 'ar'],
    'fa': ['fa', 'az', 'ar', 'en', 'ur'],

    'fr': ['fr', 'en', 'ar', 'fa'],
    'es': ['es', 'en', 'ar', 'fa'],
    'id': ['id', 'en', 'ar', 'fa'],
    'sw': ['sw', 'en', 'ar', 'fa'],
}


LANGUAGES = [
    ('ar', _('Arabic')),
    ('az', _('Azerbaijani')),
    ('fr', _('French')),
    ('in', _('Indonesia')),
    ('fa', _('Persian')),
    ('ru', _('Russia')),
    ('es', _('Spanish')),
    ('sw', _('Swahili')),
    ('tr', _('Turkish')),
    ('de', _('German')),
    ('en', _('English')),
    ('fa', _('Persian')),
    ('ur', _('Urdu')),
    ('zh', _('Mandarin')),
    ('zh', _('Chinese')),
    ('he', _('Hebrew')),
    ('he', _('Hebrew')),
    ('bn', _('Bengali')),
]

CELERY_BROKER_URL = env("REDIS_URL")
CELERY_RESULT_BACKEND = env("REDIS_URL")
CELERY_ACCEPT_CONTENT = ['application/json']
CELERY_TIMEZONE = 'Asia/Tehran'
CELERY_BROKER_TRANSPORT = 'redis'
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_BROKER_CONNECTION_RETRY_ON_STARTUP = True


# Password validation
# https://docs.djangoproject.com/en/5.0/ref/settings/#auth-password-validators


AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
        'OPTIONS': {
            'min_length': 6,
        }
    },
]


REST_FRAMEWORK = {
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.LimitOffsetPagination',
    'PAGE_SIZE': 16,
    # 'DEFAULT_AUTHENTICATION_CLASSES': [
    #     'apps.account.auth_back.TokenAuthentication2',
    # ],
    'DEFAULT_FILTER_BACKENDS': ['django_filters.rest_framework.DjangoFilterBackend'],
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework.authentication.TokenAuthentication',
        # 'rest_framework.authentication.SessionAuthentication',
    ],
    'DEFAULT_SCHEMA_CLASS': 'rest_framework.schemas.coreapi.AutoSchema',
    'EXCEPTION_HANDLER': 'utils.exceptions.exception_handler',


}
# Internationalization
# https://docs.djangoproject.com/en/5.0/topics/i18n/

LANGUAGE_CODE = 'en'

TIME_ZONE = 'Asia/Tehran'

USE_I18N = True

USE_L10N = True

USE_TZ = False

STATIC_URL = '/static/'
MEDIA_URL = '/media/'

STATICFILES_DIRS = [os.path.join(BASE_DIR, 'static')]

# STATIC_ROOT = os.path.join(BASE_DIR, 'static', 'static')
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
MEDIA_ROOT = os.path.join(BASE_DIR, 'static', 'media')

FILER_ADMIN_ICON_SIZES = ('32', '48')

FILER_ENABLE_LOGGING = True
FILER_DEBUG = True
ADMIN_TITLE = 'Golrang'
ADMIN_INDEX_TITLE = 'Golrang Administration'

# Dictionary with phone number ranges and corresponding countries
# If a country is in this dictionary, it indicates that the project's OTP service supports that country
SERVICE_OTP_COUNTRU_API_KEY = {
    "Iran": "https://console.melipayamak.com/api/send/simple/33213d78f1234e99b81f94eefda77e45"
}
SERVICE_OTP_COUNTRY_PHONE_RANGE = {
    "98": "Iran",    
    "+98": "Iran"
}

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.0/howto/static-files/


# Default primary key field type
# https://docs.djangoproject.com/en/5.0/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'
DEFAULT_SHOW_CITY_GUIDE_CITY = 'mashhad' 
FILE_UPLOAD_HANDLERS = [
    'django.core.files.uploadhandler.MemoryFileUploadHandler',
    'django.core.files.uploadhandler.TemporaryFileUploadHandler',
]
from import_export.formats import base_formats
IMPORT_EXPORT_FORMATS = [base_formats.XLSX, base_formats.CSV]


DOMAIN_POLL = "https://hamnegar.nwhco.ir/link/"