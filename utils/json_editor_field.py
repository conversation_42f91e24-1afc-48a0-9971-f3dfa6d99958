import json

from django import forms
from django.db import models


class JsonEditorWidget(forms.Textarea):
    template_name = 'fields/json_editor_field.html'


class JsonEditorField(models.JSONField):
    schema = {}

    def __init__(self, *args, schema: dict, **kwargs):
        self.schema = schema
        super().__init__(*args, **kwargs)

    def formfield(self, **kwargs):
        schema = self.schema() if callable(self.schema) else self.schema

        kwargs.update({
            'widget': JsonEditorWidget(attrs={'schema': json.dumps(schema)}),
        })
        return super(JsonEditorField, self).formfield(**kwargs)

    def deconstruct(self):
        name, path, args, kwargs = super().deconstruct()
        kwargs['schema'] = self.schema

        return name, path, args, kwargs
