from django.utils.translation import gettext_lazy as _




def default_timing():
    return {
        # "saturday": "",
        # "sunday": "",
        # "monday": "",
        # "tuesday": "",
        # "wednesday": "",
        # "thursday": "",
        # "friday": ""
    }
    
    
def get_weekly_timing_schema():
    return {
        'type': "array",
        'format': 'table',
        'title': ' ',
        'items': {
            'type': 'object',
            'title': str('Weekly Timing'),
            'properties': {
                'day': {
                    'type': 'string', 
                    'enum': ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'],
                    'title': 'Day',
                    
                },
                'time': {'type': 'string', 'format': 'time','title': str('Time')},
            }            
        }        
    }



def get_course_feature_schema():
    return {
        'type': "array",
        'format': 'table',
        'title': ' ',
        'items': {
            'type': 'object',
            'title': str(_('Course Features')),
            'properties': {
                'title': {'type': 'string', 'title': str(_('Title'))},
            }
        }
    }
