import os
from typing import Union

from django.core.files.base import ContentFile
from filer.models import Image, File, Folder
from django.core.files import File as DjangoFile


def get_django_file(path):
    return DjangoFile(open(path, mode='rb'), name=os.path.basename(path))


def import_file(file_obj: Union[str, object], folder):
    if type(file_obj) is str:
        file_obj = get_django_file(file_obj)

    if type(folder) is str:
        folder, _ = Folder.objects.get_or_create(name=folder)

    try:
        iext = os.path.splitext(file_obj.name)[1].lower()
    except:  # noqa
        iext = ''

    if iext in ['.jpg', '.jpeg', '.png', '.gif']:
        obj, created = Image.objects.get_or_create(
            original_filename=file_obj.name,
            file=file_obj,
            folder=folder,
            is_public=True
        )
    else:
        obj, created = File.objects.get_or_create(
            original_filename=file_obj.name,
            file=file_obj,
            folder=folder,
            is_public=True
        )

    return obj
