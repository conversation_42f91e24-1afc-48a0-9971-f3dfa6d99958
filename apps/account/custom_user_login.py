from django.contrib.auth.backends import BaseBackend
from django.db.models import Q

from apps.account.models import User
from utils.exceptions import UserNotFoundException
from rest_framework.exceptions import AuthenticationFailed


class CustomLoginBackend(BaseBackend):
    """
    Authenticate with username email and phone_number.
    """

    def authenticate(self, request, username=None, password=None):
        if user := self.get_user(username):
            if user.check_password(password):
                return user

        return None

    def get_user(self, username):
        try:
            return User.objects.filter(Q(email=username) | Q(phone_number=username)).first()
        except Exception.DoesNotExist:
            return None
