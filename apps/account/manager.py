
from django.contrib.auth.models import BaseUserManager
from django.contrib.auth.models import Group


from django.db.models import Manager



class UserManager(BaseUserManager):

    def create_user(
        self,
        email: str = None,
        fullname: str = None,
        password: str = None,
        **extra_fields
    ):
        email = UserManager.normalize_email(email)
        user = self.model(
            email=email,
            fullname=fullname,
            **extra_fields
        )
        user.set_password(password)
        user.save(using=self._db)
        return user
    
    def create_superuser(self, email, fullname, password):
        user = self.create_user(
            email=email,
            fullname=fullname,
            password=password,
        )
        user.is_admin = True
        user.is_staff = True
        user.is_superuser = True
        user.is_active = True
        user.user_type="super_admin"
        user.save(using=self._db)
        return user
    
    
    def change_user_type(self, user, new_user_type):
        group_name = f"{new_user_type.capitalize()} Group"
        if user.user_type != new_user_type and not user.groups.filter(name=group_name).exists():

            user.user_type = new_user_type
            new_group, _ = Group.objects.get_or_create(name=group_name)
            user.groups.add(new_group)
            user.save()
            return user
        return None     

        
    
class ProfessorUserManager(UserManager):
    def get_queryset(self):
        return super().get_queryset().filter(user_type="professor")


class ClientUserManager(UserManager):
    def get_queryset(self):
        return super().get_queryset().filter(user_type="client")

class AdminUserManager(UserManager):
    def get_queryset(self):
        return super().get_queryset().filter(user_type="admin")


class SuperAdminUserManager(UserManager):
    def get_queryset(self):
        return super().get_queryset().filter(user_type="super_admin")
    
class StudentUserManager(UserManager):
    def get_queryset(self):
        return super().get_queryset().filter(user_type="student")
