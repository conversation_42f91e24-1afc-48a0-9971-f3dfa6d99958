import random
from dj_language.field import <PERSON><PERSON>ield
from django.contrib.auth.models import AbstractUser  
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from utils.validators import validate_possible_number
from apps.account.manager import UserManager 



class User(AbstractUser):
    class GenderChoices(models.TextChoices):
        MALE = 'male', 'Male'
        FEMALE = 'female', 'Female'

    email = models.EmailField(unique=True, verbose_name="Email Address", help_text="Enter the user's email address.")
    fullname = models.CharField(max_length=255, verbose_name="Full Name", help_text="Enter the full name of the user.")
    birthdate = models.DateField(verbose_name=_('birthdate'), null=True, blank=True)
    avatar = models.ImageField(null=True, blank=True, upload_to='users/avatars/%Y/%m/')
    phone_number =  models.Char<PERSON>ield(max_length=15, verbose_name="Phone Number", help_text="Enter the user's phone number.")
    language = LanguageField(null=True)
    username = None
    last_name = None
    first_name = None
    gender = models.CharField(
        max_length=20, choices=GenderChoices.choices, null=True, blank=True, verbose_name=_('Gender'), help_text="Select the user's gender."
    )
    device_id = models.CharField(verbose_name=_('device id'), max_length=255, null=True, blank=True)
    fcm = models.CharField(max_length=512, null=True, blank=True)
    date_joined = models.DateTimeField(auto_now_add=True, verbose_name="Date Joined", help_text="The date and time the user registered.")
    is_staff = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True, verbose_name="Active", help_text="Designates whether this user should be treated as active. Unselect this instead of deleting accounts.")
    deleted_at = models.DateTimeField(null=True, blank=True)
    info = models.TextField(verbose_name="Info", null=True, blank=True)
    objects = UserManager()
          
    EMAIL_FIELD = "email"
    USERNAME_FIELD = "email"
    REQUIRED_FIELDS = ["fullname", ]

    def soft_delete(self):
        self.deleted_at = timezone.now()
        self.is_active = False
        number = str(random.randint(**********, **********))  # ایجاد یک عدد رندوم 10 رقمی
        self.phone_number = f'{self.phone_number}:deleted{number}'
        self.email = f'{self.email}:deleted{number}' if self.email else None
        self.save()
                    
    def __str__(self):
        return f"{self.email} - {self.get_full_name()}"
    

    def get_full_name(self):
        return self.fullname


    class Meta:
        ordering = ("-id",)
        verbose_name = "All Users"
        verbose_name_plural = "All Users"
