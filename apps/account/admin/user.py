from django.contrib import admin
from django.contrib.auth.forms import UserChangeForm, UsernameField
from django.contrib.auth.admin import UserAdmin
from django.utils.translation import gettext_lazy as _
from rest_framework.authtoken.models import TokenProxy
from ajaxdatatable.admin import AjaxDatatable

from apps.account.models import User
from django import forms
from django.contrib import admin
from django.urls import path, reverse
from django.shortcuts import render, redirect
from django.contrib import messages

from apps.account.models import User



@admin.register(User)    
class UserAdmin(UserAdmin, AjaxDatatable):
    list_display = (
        'email', 'fullname','last_login', 'date_joined', 
        )
    ordering = 'last_login',
    readonly_fields = ('date_joined',)
    exclude = ('password', 'user_permissions')
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('email', 'password1', 'password2'),
        }),
    )
    search_fields = (
        'email', 'fullname', 'username',
    )
    fieldsets = (
        (_('Personal info'), {'fields': ('fullname', 'email', 'phone_number', 'avatar',)}),
        (_('Permissions'), {
            'fields': ('is_active', 'is_staff', 'is_superuser', 'groups', 'password'),
        }),
        (_('Important dates'), {'fields': ('last_login', 'date_joined', 'fcm')}),
    )
    
    def save_model(self, request, obj, form, change):
        if not change:
            obj.set_password(form.cleaned_data['password1'])

        # obj.user_type = User.UserType.CLIENT
        super().save_model(request, obj, form, change)

    @admin.display(description='Phone Number')
    def _phone_number(self, obj):
        return obj.phone_number



admin.site.unregister(TokenProxy)
