from rest_framework import serializers
from rest_framework.authtoken.models import Token
from django.contrib.auth.password_validation import validate_password
from django.utils.translation import gettext_lazy as _
from apps.account.models import User
from utils import <PERSON>FieldSerializer, absolute_url



class UserProfileSerializer(serializers.ModelSerializer):
    avatar = FileFieldSerializer(required=False)
    password = serializers.CharField(write_only=True, required=False, validators=[validate_password])
    fullname = serializers.CharField(required=False)
    gender = serializers.ChoiceField(
        choices=User.GenderChoices.choices, 
        required=False,  
        help_text="Select the user's gender."  
    )
    class Meta:
        model = User
        fields = ['id', 'fullname', 'avatar', 'email', 'phone_number', 'password', 'info', 'skill', 'city', 'country', 'birthdate', 'gender']
        read_only_fields = ['email', 'info', 'skill']  

    # def validate_email(self, value):
    #     if User.objects.filter(email=value).exists():
    #         raise serializers.ValidationError("This email is already registered.")
    #     return value

    def update(self, instance, validated_data):
        password = validated_data.pop('password', None)
        if password:
            instance.set_password(password)
        # Update other fields
        for attr, value in validated_data.items():
            if value is not None:
                setattr(instance, attr, value)

        instance.save()
        return instance
