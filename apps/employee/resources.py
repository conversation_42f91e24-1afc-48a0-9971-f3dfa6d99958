
from apps.employee.models import Employee

from import_export.resources import ModelResource
from import_export import fields, widgets
from import_export.results import RowResult

        
class EmployeedResource(ModelResource):
    national_code = fields.Field(attribute='national_code', column_name='national_code')
    fullname = fields.Field(attribute='fullname', column_name='fullname')
    company = fields.Field(attribute='company', column_name='company')
    position = fields.Field(attribute='position', column_name='position')
    
    class Meta:
        skip_diff = True
        # skip_unchanged = True
        model = Employee
        fields = ('national_code', 'fullname', 'company', 'position')
        import_id_fields = ('national_code', )
        exclude = ('id',)

    def get_or_init_instance(self, instance_loader, row):
        """
        Either fetches an already existing instance or initializes a new one.
        """
        if not self._meta.force_init_instance:
            instance = self.get_instance(instance_loader, row)
            if instance:
                for attr, value in row.items():
                    setattr(instance, attr, value)

                instance.is_evaluator = True
                instance.is_active = True
                return (instance, False)
        return (self.init_instance(row), True)


    def bulk_update(self, using_transactions, dry_run, raise_errors, batch_size=None):
        print(f'-bulk_update-> {bulk_update}')
        return super().bulk_update(using_transactions, dry_run, raise_errors, batch_size)


    def before_import(self, dataset, using_transactions, dry_run, **kwargs):
        """
        Clean the dataset by removing rows with missing or empty national_code.
        """
        # Convert the dataset to a list of dictionaries for easier manipulation
        rows = dataset.dict
        cleaned_rows = []

        for row in rows:
            national_code = row.get('national_code', '').strip()  # Get and strip whitespace
            if national_code:  # Keep only rows with non-empty national_code
                cleaned_rows.append(row)
            else:
                print(f"Removing row with missing or empty national_code: {row}")

        # Replace the dataset with the cleaned rows
        dataset.dict = cleaned_rows
        return super().before_import(dataset, using_transactions, dry_run, **kwargs)


    def init_instance(self, row=None):
        national_code = row.get('national_code')
        print(f'-init_instance--> {national_code}')
        
        # Skip the row if national_code is missing
        if not national_code:
            print(f"Skipping row due to missing national_code: {row}")
            return 
        
        # Fetch or create the Employee instance
        instance = Employee.objects.filter(national_code=national_code).first()
        if not instance:
            instance = self._meta.model()

        instance.is_evaluatee = True
        instance.is_active = True        
        # Set attributes from the row
        for attr, value in row.items():
            setattr(instance, attr, value)
        instance.save()  
            
        return instance    
    
    def after_import(self, dataset, result, using_transactions, dry_run, **kwargs):
        imported_national_codes = set(row['national_code'] for row in dataset.dict if row.get('national_code'))
        Employee.objects.exclude(national_code__in=imported_national_codes).update(is_active=False)
        
        super().after_import(dataset, result, using_transactions, dry_run, **kwargs)