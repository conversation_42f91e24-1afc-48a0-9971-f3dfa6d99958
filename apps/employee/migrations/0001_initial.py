# Generated by Django 3.2.4 on 2025-02-01 23:00

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Employee',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('fullname', models.<PERSON><PERSON><PERSON><PERSON>(max_length=200)),
                ('email', models.EmailField(max_length=254, unique=True)),
                ('national_code', models.Char<PERSON>ield(max_length=15, unique=True, verbose_name='national code')),
                ('phone_number', models.Char<PERSON>ield(max_length=15, verbose_name='phone number')),
                ('company', models.Char<PERSON>ield(max_length=250, verbose_name='company')),
                ('position', models.Char<PERSON>ield(max_length=250, verbose_name='position')),
                ('address', models.TextField(verbose_name='address')),
            ],
            options={
                'verbose_name': 'Employee',
                'verbose_name_plural': 'Employees',
            },
        ),
    ]
