from celery import shared_task

import time
import requests
import zeep
import json

@shared_task
def send_sms(employee_data, sms_message):
    client = zeep.Client('http://payamak-service.ir/SendService.svc?wsdl')
    username = "mt.daroueigolrang"
    password = "48750"
    from_number = "2124852000"
    
    for data in employee_data:
        phone_number = data['phone_number']
        link = data['link']
        fullname = data['fullname']
        # message = f"سلام {fullname},\nلطفاً از لینک زیر برای تکمیل نظرسنجی استفاده کنید:\n{link}\nلغو11"
        message = f"{sms_message}\n{link}\nلغو11"

        if phone_number and phone_number[0] != '0':  # اگر رقم اول صفر نباشد
            phone_number = '0' + phone_number
        print(f'--->{phone_number}')
        # پارامترهای ارسال پیامک
        parameters = {
            'userName': username,
            'password': password,
            'fromNumber': from_number,
            'toNumbers': [phone_number],  
            'messageContent': message,
            'isFlash': False,
        }

        try:
            # ارسال پیامک
            response = client.service.SendSMS(**parameters)
            print(f'--sending--> {fullname}--res--> {response.SendSMSResult}')
        except Exception as e:
            print(f"---Exception--code--> : {fullname}: {str(e)}")


# if __name__ == "__main__":
#     employee_data = [
#         {"phone_number": "09012045375", "link": "https://example.com/poll/123456",  "fullname": "alireza mortezaei"},
#         {"phone_number": "09012045375", "link": "https://example.com/poll/1232323456",  "fullname": "alireza mortezaei 2"},
#     ]
#     send_sms(employee_data)