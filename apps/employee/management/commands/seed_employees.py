import random
import pandas as pd
from django.core.management.base import BaseCommand
from faker import Faker


class Command(BaseCommand):
    help = 'Generate Excel file with test employees data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--count',
            type=int,
            default=1000,
            help='Number of employees to generate (default: 1000)'
        )
        parser.add_argument(
            '--output',
            type=str,
            default='test_employees.xlsx',
            help='Output Excel file name (default: test_employees.xlsx)'
        )

    def handle(self, *args, **options):
        count = options['count']
        output_file = options['output']
        
        fake = Faker('fa_IR')  # Persian fake data
        
        # لیست شرکت‌ها و سمت‌های نمونه
        companies = [
            'داده پرداز افق نو',
            'فناوری اطلاعات پارس',
            'صنایع نرم‌افزاری ایران',
            'شرکت توسعه فناوری',
            'مهندسی نرم‌افزار آسیا',
            'فناوری‌های نوین دیجیتال',
            'سیستم‌های هوشمند ایران',
            'شرکت نوآوری تکنولوژی',
            'داده‌کاوی پیشرفته',
            'فناوری اطلاعات همراه'
        ]
        
        positions = [
            'توسعه‌دهنده فرانت‌اند',
            'توسعه‌دهنده بک‌اند', 
            'مهندس DevOps',
            'طراح UI/UX',
            'مدیر پروژه',
            'تحلیلگر سیستم',
            'مهندس کیفیت نرم‌افزار',
            'متخصص امنیت سایبری',
            'مهندس داده',
            'توسعه‌دهنده فلاتر',
            'توسعه‌دهنده React Native',
            'برنامه‌نویس Python',
            'برنامه‌نویس Java',
            'برنامه‌نویس .NET',
            'مهندس یادگیری ماشین',
            'متخصص هوش مصنوعی',
            'مدیر محصول',
            'کارشناس تست نرم‌افزار',
            'مهندس شبکه',
            'مدیر فنی'
        ]

        self.stdout.write(
            self.style.SUCCESS(f'Generating {count} test employees Excel file...')
        )

        employees_data = []
        used_national_codes = set()
        
        for i in range(count):
            # ایجاد کد ملی یکتا
            while True:
                national_code = fake.ssn()
                if national_code not in used_national_codes:
                    used_national_codes.add(national_code)
                    break
            
            # ایجاد شماره موبایل فارسی
            phone_number = f"0919{random.randint(1000000, 9999999)}"
            
            employee_data = {
                'fullname': fake.name(),
                'email': fake.email(),
                'national_code': national_code,
                'phone_number': phone_number,
                'company': random.choice(companies),
                'position': random.choice(positions),
                'address': fake.address(),
                'is_evaluator': random.choice([True, False]) if i % 5 != 0 else True,  # 80% evaluator
                'is_active': True
            }
            
            employees_data.append(employee_data)
            
            # نمایش پیشرفت هر 100 رکورد
            if (i + 1) % 100 == 0:
                self.stdout.write(
                    self.style.SUCCESS(f'Generated {i + 1}/{count} employees...')
                )

        # ایجاد DataFrame و ذخیره در Excel
        try:
            df = pd.DataFrame(employees_data)
            df.to_excel(output_file, index=False, engine='openpyxl')
                
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully created Excel file: {output_file}'
                    f'\n- Total records: {len(df)}'
                    f'\n- Evaluators: {df[df["is_evaluator"] == True].shape[0]}'
                    f'\n- Non-evaluators: {df[df["is_evaluator"] == False].shape[0]}'
                    f'\n- Unique companies: {df["company"].nunique()}'
                    f'\n- Unique positions: {df["position"].nunique()}'
                )
            )
            
            # نمایش نمونه داده‌ها
            self.stdout.write(
                self.style.SUCCESS('\nSample data (first 3 rows):')
            )
            self.stdout.write(str(df.head(3)))
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error creating Excel file: {str(e)}')
            )