from django.db.models.signals import pre_save
from django.dispatch import receiver
from apps.employee.models import Employee
from apps.evaluation.models import PollAssignment


@receiver(pre_save, sender=Employee)
def deactivate_poll_assignments(sender, instance, **kwargs):
    if instance.pk:  
        previous = Employee.objects.get(pk=instance.pk)
        if not instance.is_active and previous.is_active:  
            PollAssignment.objects.filter(assigned_to=instance).update(is_active=False)

