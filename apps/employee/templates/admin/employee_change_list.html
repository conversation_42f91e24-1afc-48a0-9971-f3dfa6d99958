{% extends "admin/change_list_ajax.html" %}
{% load i18n %}
{% load admin_urls %}

{% block layout-buttons %}
    {% if has_add_permission %}
        <button type="button" class="float-right btn bg-indigo-400 legitRipple mr-3 ml-3 btn btn-light"
                data-toggle="modal" data-target="#modal_default">
            {% trans "SEND SMS" %} <i class="icon-play3 ml-2"></i>
        </button>
    {% endif %}

    {% comment %} <a href="{% url opts|admin_urlname:'export' %}{{ cl.get_query_string }}"
       class="btn bg-info legitRipple mx-3">
        {% trans "Export" %}
        <i class="icon-database-export"></i>
    </a> {% endcomment %}

    {% if has_import_permission %}
        <a href='{% url opts|admin_urlname:"import" %}'
           class="btn bg-info legitRipple mx-3">
            {% trans "Import" %}
            <i class="icon-database-insert"></i>
        </a>
    {% endif %}

    {{ block.super }}
{% endblock %}

{% block content %}
    {{ block.super }}
    <div id="modal_default" class="modal fade" tabindex="-1">
        <div class="modal-dialog">
            <form method="post" action="{% url cl.opts|admin_urlname:'send_sms' %}">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">{% trans "Send SMS" %}</h5>
                        <button type="button" class="close text-white" data-dismiss="modal">&times;</button>
                    </div>

                    <div class="modal-body">
                        {% csrf_token %}
                        <div class="form-group">
                            <label for="send_to">{% trans "Send To:" %}</label>
                            <select class="form-control" name="send_to" id="send_to">
                                <option value="all">{% trans "All Employees in Poll" %}</option>
                                <option value="manual">{% trans "Manual by National Code" %}</option>
                            </select>
                        </div>

                        <div id="national_code_field" class="form-group mt-3" style="display: none;">
                            <label for="national_codes">{% trans "National Codes (comma separated):" %}</label>
                            <input name="national_codes" class="form-control" type="text" placeholder="{% trans 'Enter national codes' %}">
                        </div>

                        <div class="form-group">
                            <label for="poll_id">{% trans "Poll:" %}</label>
                            <select class="form-control" name="poll_id" id="poll_id">
                                {% for poll in polls %}
                                    <option value="{{ poll.id }}">{{ poll.name }}</option>
                                {% endfor %}
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="sms_message">{% trans "Message Text:" %}</label>
                            <textarea name="sms_message" class="form-control" rows="4" placeholder="{% trans 'Enter the SMS message text' %}"></textarea>
                            <small class="text-danger mt-2 d-block">
                                {% trans "Note: The survey link will be added at the end of the message." %}
                            </small>
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">{% trans "Close" %}</button>
                        <button type="submit" class="btn btn-primary">{% trans "Send SMS" %}</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const sendToSelect = document.getElementById('send_to');
            const nationalCodeField = document.getElementById('national_code_field');

            sendToSelect.addEventListener('change', function() {
                nationalCodeField.style.display = this.value === 'manual' ? 'block' : 'none';
            });

            if (sendToSelect.value === 'manual') {
                nationalCodeField.style.display = 'block';
            }
        });
    </script>
{% endblock %}