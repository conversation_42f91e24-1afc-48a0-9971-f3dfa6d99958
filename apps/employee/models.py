from django.db import models




class Employee(models.Model):
    fullname = models.Char<PERSON>ield(max_length=200)
    email = models.EmailField(max_length=254, null=True, blank=True)
    national_code = models.CharField(max_length=15, unique=True, verbose_name='national code')
    phone_number = models.CharField(max_length=15, verbose_name='phone number', null=True, blank=True)
    company = models.CharField(max_length=250, verbose_name='company',null=True, blank=True)
    position = models.CharField(max_length=250, verbose_name='position', null=True, blank=True)
    address = models.TextField(verbose_name='address', null=True, blank=True)
    is_evaluator = models.BooleanField(default=True, verbose_name='is evaluator')
    
    is_active = models.BooleanField(default=True, verbose_name='is active')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='created at')
    
    
    def __str__(self):
        return f'{self.fullname} - {self.national_code}'


    class Meta:
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'