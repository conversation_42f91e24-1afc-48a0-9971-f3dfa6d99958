
from django.contrib import admin, messages
from apps.employee.models import Employee
from django.shortcuts import redirect
from django.urls import path, reverse
from ajaxdatatable.admin import AjaxDatatable, AjaxDatatableWithImportExport
from ajaxdatatable.import_mixin import ImportMixin
from django.utils.translation import gettext_lazy as _
from apps.employee.resources import EmployeedResource

from import_export import fields, widgets
from apps.evaluation.models import Poll, PollAssignment
from apps.employee.tasks import send_sms
from config.settings import base as settings
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from django.template.response import TemplateResponse
import uuid
import os
from apps.evaluation.tasks import process_employee_import_file


@admin.register(Employee) 
class EmployeeAdmin(ImportMixin, AjaxDatatableWithImportExport):
    list_display = ('fullname', 'national_code', 'company', 'position', 'phone_number')
    search_fields = ('fullname', 'national_code')
    list_filter = ('company', 'position')
    resource_class = EmployeedResource
    change_list_template = 'admin/employee_change_list.html'
    
    fieldsets = (
        ('Personal Information', {
            'fields': ('fullname', 'email', 'national_code', 'phone_number', 'address')
        }),
        ('Job Information', {
            'fields': ('company', 'position',)
        }),
    )
    
    def get_export_formats(self):
        return []
    def has_export_permission(self, request):
        return False
    
    def get_import_formats(self):
        exclude_formats = ['yaml', 'tsv', 'json', 'ods', 'html',]
        return [f for f in self.formats if f().can_export() if not f().get_title() in exclude_formats]
    
    def import_action(self, request, *args, **kwargs):
        context = self.get_import_context_data()
        import_formats = self.get_import_formats()
        form_type = self.get_import_form()
        form_kwargs = self.get_form_kwargs(form_type, *args, **kwargs)
        form = form_type(import_formats,
                         request.POST or None,
                         request.FILES or None,
                         **form_kwargs)

        if request.POST and form.is_valid():
            import_file = form.cleaned_data['import_file']
            
            try:
                # ایجاد نام یکتا برای فایل
                file_extension = os.path.splitext(import_file.name)[1]
                unique_filename = f"employee_import_{uuid.uuid4().hex}{file_extension}"
                file_path = f"temp_imports/{unique_filename}"
                
                # ذخیره فایل در storage
                saved_path = default_storage.save(file_path, ContentFile(import_file.read()))
                
                # اجرای تسک Celery
                task = process_employee_import_file.delay(
                    file_path=saved_path,
                    user_id=request.user.id if request.user.is_authenticated else None
                )
                
                # نمایش پیام موفقیت
                messages.success(
                    request, 
                    _(f"File uploaded successfully. Import process started in background. Task ID: {task.id}")
                )
                
                return redirect('admin:employee_employee_changelist')
                
            except Exception as e:
                messages.error(request, _(f"Error processing import file: {str(e)}"))
                return redirect('admin:employee_employee_changelist')
        else:
            res_kwargs = self.get_import_resource_kwargs(request, form=form, *args, **kwargs)
            resource = self.get_import_resource_class()(**res_kwargs)

        context.update(self.admin_site.each_context(request))

        context['title'] = _("Import")
        context['form'] = form
        context['opts'] = self.model._meta
        context['fields'] = [f.column_name for f in resource.get_user_visible_fields()]

        request.current_app = self.admin_site.name
        return TemplateResponse(request, [self.import_template_name],
                                context)
    
    def changelist_view(self, request, extra_context=None):
        extra_context = {
            'polls': Poll.objects.filter(is_active=True), 
        }
        return super().changelist_view(request, extra_context)

    def get_queryset(self, request):
        return super().get_queryset(request).filter(is_active=True, is_evaluator=True)

    def delete_queryset(self, request, queryset):
        queryset.update(is_active=False)

    def delete_model(self, request, obj):
        obj.is_active = False
        obj.save()
        
    def save_model(self, request, obj, form, change):
        if not change:
            obj.is_evaluator = True  
        super().save_model(request, obj, form, change)
            
    def get_urls(self):
        info = self.model._meta.app_label, self.model._meta.model_name
        urls = [
            path(
                'send-sms/',
                self.admin_site.admin_view(self.send_sms_employee),
                name='%s_%s_send_sms' % info
            )
        ]
        return urls + super().get_urls()
    
    
    def send_sms_employee(self, request):
        if not request.POST:
            return redirect(reverse('admin:employee_employee_changelist'))
        try:
            send_to = request.POST.get('send_to')
            poll_id = request.POST.get('poll_id')
            sms_message = request.POST.get('sms_message', 'test')  
            national_codes = request.POST.get('national_codes', '')
            print(f'---> {send_to} // {poll_id}, national_codes: {type(national_codes)}/ {national_codes}')
            try:
                poll = Poll.objects.get(id=poll_id)
            except Poll.DoesNotExist:
                messages.error(request, "نظرسنجی یافت نشد")
                return redirect(reverse('admin:employee_employee_changelist'))

    
            employee_data = []

            if send_to == 'all':
                # همه کارمندانی که به این نظرسنجی اختصاص داده شده‌اند
                assignments = PollAssignment.objects.filter(poll=poll, is_active=True)
                for assignment in assignments:
                    employee = assignment.assigned_to
                    if employee.is_active and employee.is_evaluator and employee.phone_number:
                        # ساخت لینک کامل با access_token
                        link = f"{settings.DOMAIN_POLL}{assignment.access_token}"  # لینک نمونه

                        employee_data.append({
                            'phone_number': employee.phone_number,
                            "fullname": employee.fullname,
                            'link': link
                        })
            elif national_codes:
                # کارمندانی با کد ملی مشخص
                national_codes_list = [code.strip() for code in national_codes.split(',')]
                employees = Employee.objects.filter(national_code__in=national_codes_list)
                for employee in employees:
                    if employee.is_active and employee.is_evaluator and employee.phone_number:
                        # پیدا کردن assignment مربوطه برای این کارمند و نظرسنجی
                        assignment = PollAssignment.objects.filter(poll=poll, assigned_to=employee, is_active=True).first()
                        if assignment:
                            # ساخت لینک کامل با access_token
                            link = f"{settings.DOMAIN_POLL}{assignment.access_token}"  # لینک نمونه

                            employee_data.append({
                                'phone_number': employee.phone_number,
                                "fullname": employee.fullname,
                                'link': link
                            })

            if employee_data:
                send_sms.delay(employee_data, sms_message)  # ارسال پیامک‌ها به صورت async
                print(f'employee_data--> {employee_data}')                
                messages.success(request, "در حال ارسال پیامک‌ها به کارمندان...")
            else:
                messages.warning(request, "هیچ شماره تلفنی برای ارسال پیامک یافت نشد.")
                
        except Exception as exp:
            print(f'-Exception--> {exp}')
            messages.error(request, f"خطا در ارسال پیامک: {str(exp)}")

        return redirect(reverse('admin:employee_employee_changelist'))
    
    

