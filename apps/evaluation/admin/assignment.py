from django.contrib import admin, messages
from ajaxdatatable.admin import AjaxDatatable, AjaxDatatableWithImportExport, AjaxDatatableWithExcelExport
from django.utils.translation import gettext_lazy as _
from ajaxdatatable.import_mixin import ImportMixin
from datetime import datetime
from django.utils.safestring import mark_safe
from django.utils.text import slugify  
from unidecode import unidecode
from django.http import HttpResponse, HttpResponseRedirect
from django.template.response import TemplateResponse
from django.utils.encoding import force_str
from django.shortcuts import redirect
from config.settings import base as settings
from django.utils.encoding import smart_str    
from import_export.signals import post_export
from django.db.models import Min
from django import forms
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
import uuid
import os

from apps.evaluation.resources import *
from apps.evaluation.forms import *
from apps.evaluation.models import *
from apps.evaluation.tasks import process_assignment_import_file



class SelectableEmployeeInline(admin.TabularInline):
    model = SelectableEmployee
    fields = ('employee', 'priority')
    fk_name = 'poll_assignment'
    form = SelectableEmployeeInlineForm
    extra = 0 
    autocomplete_fields = ['employee', ]

    def get_fields(self, request, obj=None):
        return ('employee', 'priority') # example.  Specify fields in your model.

        


        

 
@admin.register(PollAssignment)
class PollAssignmentAdmin(ImportMixin, AjaxDatatableWithImportExport):
    form = PollAssignmentForm
    list_display = ('poll', 'assigned_to', '_link', 'created_at', '_selected_employees')
    search_fields = ('assigned_to__national_code', 'assigned_to__fullname', 'poll__name')
    list_filter = ('poll',)
    resource_class = PollAssignmentResource
    change_list_template = 'admin/assignment_change_list.html'
    readonly_fields = ('access_token',)
    inlines = [SelectableEmployeeInline, ] 
    autocomplete_fields = ['assigned_to', 'poll', ]

    fieldsets = (
        ('Assignment Details', {
            'fields': ('poll', 'assigned_to', 'access_token')
        }),
    )

    
    @admin.display(description=_('Link'), ordering='access_token')
    def _link(self, obj):
        return f"{settings.DOMAIN_POLL}{obj.access_token}"

    @admin.display(description=_('Selectable Employees'))
    def _selected_employees(self, obj):
        try:
            selectable_employees = SelectableEmployee.objects.filter(poll_assignment=obj)
            employee_names = [employee.employee.fullname for employee in selectable_employees]
            return mark_safe(" | ".join(employee_names) if employee_names else '-')
        except Exception as e:
            print(f'-_selected_employees-> {e}')
            return "-"
        
    def get_queryset(self, request):
        return super().get_queryset(request).filter(is_active=True)

    def delete_queryset(self, request, queryset):
        queryset.update(is_active=False)

    def delete_model(self, request, obj):
        obj.is_active = False
        obj.save()


    def get_import_form(self):
        return ImportForm


    def get_confirm_import_form(self):
        return None
    
    def get_import_formats(self):
        exclude_formats = ['yaml', 'tsv', 'json', 'ods', 'html',]
        return [f for f in self.formats if f().can_export() if not f().get_title() in exclude_formats]

    def get_export_formats(self):
        exclude_formats = ['yaml', 'xls']
        return [f for f in self.formats if f().can_export() if not f().get_title() in exclude_formats]


    def get_export_queryset(self, request):
        print(f'-1-get_export_queryset-> ================================')
        queryset = super().get_export_queryset(request)
        poll_id = request.POST.get('poll')  # شناسه Poll انتخاب‌شده از فرم
        if not poll_id:
            return PollAssignmentResource.objects.none()
        queryset = queryset.filter(poll__id=poll_id, is_active=True, assigned_to__is_evaluator=True, assigned_to__is_active=True)
        print(f'-2-get_export_queryset-> {poll_id}/: {queryset}')

        return queryset


    def export_action(self, request, *args, **kwargs):
        print(f'--1---export_action--> request:{request}/args: {args}/ kwargs: {kwargs}')
        if not self.has_export_permission(request):
            raise PermissionDenied

        formats = self.get_export_formats()
        form = PollAssignmentExportForm(formats, request.POST or None)
        if form.is_valid():
            file_format = formats[
                int(form.cleaned_data['file_format'])
            ]()

            queryset = self.get_export_queryset(request)
            # بررسی خالی بودن queryset
            if not queryset:
                messages.error(request, _("No data available to export."))
                return redirect('admin:evaluation_pollassignment_changelist')
            
            export_data = self.get_export_data(file_format, queryset, request=request)
            content_type = file_format.get_content_type()
            response = HttpResponse(export_data, content_type=content_type)

            print(f'--=====export_action======----{queryset}')
            response = queryset[0]
            poll = response.poll
            if poll:
                print(f'-poll->{poll.name}')
                # poll_name_slug = slugify(poll.name, allow_unicode=True)  # تبدیل نام Poll به اسلاگ
                # print(f'poll_name_slug--> {poll_name_slug}')
                filename = f"employee_survey_links{int(poll.id)}.{file_format.get_extension()}"
                print(f'filename--> {filename}')
            else:
                filename = self.get_export_filename(request, queryset, file_format)

            response = HttpResponse(export_data, content_type=content_type)
            response['Content-Disposition'] = f'attachment; filename="{filename}"'

            post_export.send(sender=None, model=self.model)
            return response

        context = self.get_export_context_data()

        context.update(self.admin_site.each_context(request))

        context['title'] = _("Export")
        context['form'] = form
        context['opts'] = self.model._meta
        request.current_app = self.admin_site.name
        return TemplateResponse(request, [self.export_template_name],
                                context)




    def get_import_data_kwargs(self, request, *args, **kwargs):
        kwargs = super().get_import_data_kwargs(request, *args, **kwargs)        
        form = ImportForm(self.get_import_formats(), request.POST, request.FILES,)
        if isinstance(form, ImportForm): # بررسی نوع فرم
            poll_id = form.get_poll_id()  # دریافت ID نظرسنجی از فرم ImportForm            
        kwargs['poll'] = poll_id
        return kwargs
    
    def import_action(self, request, *args, **kwargs):
        context = self.get_import_context_data()
        import_formats = self.get_import_formats()
        form_type = self.get_import_form()
        form_kwargs = self.get_form_kwargs(form_type, *args, **kwargs)
        form = form_type(import_formats,
                         request.POST or None,
                         request.FILES or None,
                         **form_kwargs)

        if request.POST and form.is_valid():
            import_file = form.cleaned_data['import_file']
            poll_id = form.cleaned_data['poll'].id
            
            try:
                # ایجاد نام یکتا برای فایل
                file_extension = os.path.splitext(import_file.name)[1]
                unique_filename = f"import_{uuid.uuid4().hex}{file_extension}"
                file_path = f"temp_imports/{unique_filename}"
                
                # ذخیره فایل در storage
                saved_path = default_storage.save(file_path, ContentFile(import_file.read()))
                
                # اجرای تسک Celery
                task = process_assignment_import_file.delay(
                    file_path=saved_path,
                    poll_id=poll_id,
                    user_id=request.user.id if request.user.is_authenticated else None
                )
                
                # نمایش پیام موفقیت
                messages.success(
                    request, 
                    _(f"File uploaded successfully. Import process started in background. Task ID: {task.id}")
                )
                
                return redirect('admin:evaluation_pollassignment_changelist')
                
            except Exception as e:
                messages.error(request, _(f"Error processing import file: {str(e)}"))
                return redirect('admin:evaluation_pollassignment_changelist')
        else:
            res_kwargs = self.get_import_resource_kwargs(request, form=form, *args, **kwargs)
            resource = self.get_import_resource_class()(**res_kwargs)

        context.update(self.admin_site.each_context(request))

        context['title'] = _("Import")
        context['form'] = form
        context['opts'] = self.model._meta
        context['fields'] = [f.column_name for f in resource.get_user_visible_fields()]

        request.current_app = self.admin_site.name
        return TemplateResponse(request, [self.import_template_name],
                                context)
        
    def has_export_permission(self, request):
        return True

    




