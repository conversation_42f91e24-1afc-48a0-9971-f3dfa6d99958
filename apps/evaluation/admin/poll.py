
from django.contrib import admin
from ajaxdatatable.admin import AjaxDatatable
from apps.evaluation.models import *
from datetime import datetime


class PollAdmin(AjaxDatatable):
    list_display = ('name', 'created_at', 'is_active', 'jalali_start_date')
    list_filter = ('is_active',)
    search_fields = ('name',)
    ordering = ('-created_at',)

    def delete_model(self, request, obj):
        obj.is_active = False
        obj.save()

    def jalali_start_date(self, obj):
        return obj.jalali_start_date or "N/A"
    
admin.site.register(Poll, PollAdmin)




