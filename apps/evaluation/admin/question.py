from django.contrib import admin, messages
from ajaxdatatable.admin import AjaxDatatable, AjaxDatatableWithImportExport, AjaxDatatableWithExcelExport
from django.utils.translation import gettext_lazy as _
from ajaxdatatable.import_mixin import ImportMixin
from apps.evaluation.resources import *
from apps.evaluation.models import *
from datetime import datetime
from django.utils.safestring import mark_safe
from django.utils.text import slugify  
from unidecode import unidecode
from django.http import HttpResponse, HttpResponseRedirect
from django.template.response import TemplateResponse
from django.utils.encoding import force_str
from django.shortcuts import redirect
from config.settings import base as settings
from django.utils.encoding import smart_str    
from import_export.signals import post_export
from django.db.models import Min
from apps.evaluation.forms import *
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
import uuid
import os
from apps.evaluation.tasks import process_question_import_file





@admin.register(Question)
class QuestionAdmin(ImportMixin, AjaxDatatableWithImportExport):
    list_display = ('text', 'category', 'poll', 'question_order', 'is_active')
    search_fields = ('text', 'category')
    list_filter = ('poll', 'category')
    resource_class = QuestionResource
    ordering = ('question_order',) 

    def has_export_permission(self, request):
        return False

    def delete_model(self, request, obj):
        obj.is_active = False
        obj.save()
 
    def get_import_form(self):
        return ImportForm

    def get_confirm_import_form(self): # اضافه کردن این تابع
        return None


    def get_import_formats(self):
        """
            Returns available export formats.
        """
        exclude_formats = ['yaml', 'tsv', 'json', 'ods', 'html',]
        return [f for f in self.formats if f().can_export() if not f().get_title() in exclude_formats]

    def get_import_data_kwargs(self, request, *args, **kwargs):
        kwargs = super().get_import_data_kwargs(request, *args, **kwargs)        
        form = ImportForm(self.get_import_formats(), request.POST, request.FILES,)
        if isinstance(form, ImportForm): # بررسی نوع فرم
            poll_id = form.get_poll_id()  # دریافت ID نظرسنجی از فرم ImportForm            
        kwargs['poll'] = poll_id
        return kwargs

    def import_action(self, request, *args, **kwargs):
        context = self.get_import_context_data()
        import_formats = self.get_import_formats()
        form_type = self.get_import_form()
        form_kwargs = self.get_form_kwargs(form_type, *args, **kwargs)
        form = form_type(import_formats,
                         request.POST or None,
                         request.FILES or None,
                         **form_kwargs)

        if request.POST and form.is_valid():
            import_file = form.cleaned_data['import_file']
            poll_id = form.cleaned_data['poll'].id
            
            try:
                # ایجاد نام یکتا برای فایل
                file_extension = os.path.splitext(import_file.name)[1]
                unique_filename = f"question_import_{uuid.uuid4().hex}{file_extension}"
                file_path = f"temp_imports/{unique_filename}"
                
                # ذخیره فایل در storage
                saved_path = default_storage.save(file_path, ContentFile(import_file.read()))
                
                # اجرای تسک Celery
                task = process_question_import_file.delay(
                    file_path=saved_path,
                    poll_id=poll_id,
                    user_id=request.user.id if request.user.is_authenticated else None
                )
                
                # نمایش پیام موفقیت
                messages.success(
                    request, 
                    _(f"File uploaded successfully. Import process started in background. Task ID: {task.id}")
                )
                
                return redirect('admin:evaluation_question_changelist')
                
            except Exception as e:
                messages.error(request, _(f"Error processing import file: {str(e)}"))
                return redirect('admin:evaluation_question_changelist')
        else:
            res_kwargs = self.get_import_resource_kwargs(request, form=form, *args, **kwargs)
            resource = self.get_import_resource_class()(**res_kwargs)

        context.update(self.admin_site.each_context(request))

        context['title'] = _("Import")
        context['form'] = form
        context['opts'] = self.model._meta
        context['fields'] = [f.column_name for f in resource.get_user_visible_fields()]

        request.current_app = self.admin_site.name
        return TemplateResponse(request, [self.import_template_name],
                                context)


