from django.contrib import admin, messages
from ajaxdatatable.admin import AjaxDatatable, AjaxDatatableWithImportExport, AjaxDatatableWithExcelExport
from django.utils.translation import gettext_lazy as _
from ajaxdatatable.import_mixin import ImportMixin
from datetime import datetime
from django.utils.safestring import mark_safe
from django.utils.text import slugify  
from unidecode import unidecode
from django.http import HttpResponse, HttpResponseRedirect
from django.template.response import TemplateResponse
from django.utils.encoding import force_str
from django.shortcuts import redirect
from config.settings import base as settings
from django.utils.encoding import smart_str    
from import_export.signals import post_export
from django.db.models import Min
from collections import defaultdict

from apps.evaluation.resources import *
from apps.evaluation.models import *
from apps.evaluation.forms import *

        
        
@admin.register(Response)
class ResponseAdmin(AjaxDatatableWithExcelExport):
    form = ResponseForm
    import_template_name = None
    import_fields = []
    import_id_fields = []
    resource_class = ResponseResource
    autocomplete_fields = ('poll_assignment', 'question', 'selected_employee', 'evaluator')
    
    list_display = ('question', 'selected_employee', 'evaluator', 'rating', 'created_at', 'updated_at')
    list_filter = ('poll_assignment', 'question', 'selected_employee', 'evaluator', 'created_at')
    search_fields = ('poll_assignment__poll__name', 'question__text', 'selected_employee__fullname', 'evaluator__fullname', 'rating')
    ordering = ('-created_at',)

    def has_import_permission(self, obj):
        return False
    
    def get_export_formats(self):
        exclude_formats = ['yaml', 'xls']
        return [f for f in self.formats if f().can_export() if not f().get_title() in exclude_formats]

    def get_export_filename(self, request, queryset, file_format):
        return super().get_export_filename(request, queryset, file_format)

    def get_export_queryset(self, request):
        print(f'-2-get_export_queryset-> ================================')

        poll_id = request.POST.get('poll')  # شناسه Poll انتخاب‌شده از فرم
        grouping_type = request.POST.get('grouping_type', 'evaluatee')
        if not poll_id:
            return Response.objects.none()

        poll_assignments = PollAssignment.objects.filter(poll_id=poll_id)
    
        # لیست نهایی برای خروجی
        export_data = []
    
        # لیست موقت برای نگهداری رکوردهای "شرکت نکرده است"
        no_response_data = []
    
        for assignment in poll_assignments:
            if not assignment.responses.exists():
                # اگر هیچ Response وجود نداشت، یک رکورد ساختگی ایجاد کنید
                fake_response = Response(
                    poll_assignment=assignment,
                    question=assignment.poll.questions.first(),  # اولین سوال Poll
                    evaluator=assignment.assigned_to,  # Evaluator همان assigned_to است
                    selected_employee=None,  # چون هیچ Response وجود ندارد
                    rating=None,
                    comment=None,
                    created_at=assignment.created_at,  # زمان ایجاد PollAssignment
                    updated_at=assignment.updated_at  # زمان به‌روزرسانی PollAssignment
                )
                no_response_data.append(fake_response)
            else:
                # اگر Response وجود دارد، همه Responseهای مرتبط را اضافه کنید
                export_data.extend(assignment.responses.all())
    
        if grouping_type == 'evaluatee':
            # گروه بندی ارزیابی شونده
            export_data.sort(key=lambda x: (
                x.selected_employee.id if x.selected_employee else float('inf'),
                x.evaluator.id,
                x.question.question_order if x.question else 0
            ))
            
        else:
            # گروه بندی ارزیابی کننده
            export_data.sort(key=lambda x: (
                x.evaluator.id,
                x.selected_employee.id if x.selected_employee else float('inf'),
                x.question.question_order if x.question else 0
            ))

        # ساختار گروه بندی نهایی
        grouped_responses = defaultdict(lambda: defaultdict(list))
        for response in export_data:
            if grouping_type == 'evaluatee':
                primary_key = response.selected_employee.id if response.selected_employee else None
                secondary_key = response.evaluator.id
            else:
                primary_key = response.evaluator.id
                secondary_key = response.selected_employee.id if response.selected_employee else None
            
            grouped_responses[primary_key][secondary_key].append(response)

        # تبدیل گروه بندی به لیست نهایی
        final_export = []
        for primary_group in grouped_responses.values():
            for secondary_group in primary_group.values():
                # مرتب سازی سوالات بر اساس question_order
                secondary_group.sort(key=lambda r: r.question.question_order if r.question else 0)
                final_export.extend(secondary_group)
        
        # اضافه کردن ناپاسخ داده ها
        final_export.extend(no_response_data)
        return final_export

        # export_data.sort(key=lambda x: (
        #     x.selected_employee.id if x.selected_employee else float('inf'),  # امتیازات اولویت داده شده به evaluateeهایی که وجود دارند
        #     x.evaluator.id,
        #     x.question.question_order if x.question else 0  # اطمینان از وجود question
        # ))    
        # # گروه‌بندی پاسخ‌ها بر اساس Evaluator و Evaluatee
        # grouped_data = []
        # current_evaluator = None
        # current_evaluatee = None
        # group = []
    
        # for response in export_data:
        #     if response.evaluator != current_evaluator or response.selected_employee != current_evaluatee:
        #         if group:
        #             grouped_data.extend(group)
        #             group = []
        #         current_evaluator = response.evaluator
        #         current_evaluatee = response.selected_employee
        #     group.append(response)
    
        # if group:
        #     grouped_data.extend(group)
    
        # # اضافه کردن رکوردهای "شرکت نکرده است" به انتهای لیست
        # grouped_data.extend(no_response_data)
    
        # return grouped_data    
    
    def export_action(self, request, *args, **kwargs):
        print(f'--1---export_action--> request:{request}/args: {args}/ kwargs: {kwargs}')
        if not self.has_export_permission(request):
            raise PermissionDenied

        formats = self.get_export_formats()
        form = ResponseExportForm(formats, request.POST or None)
        if form.is_valid():
            file_format = formats[
                int(form.cleaned_data['file_format'])
            ]()

            queryset = self.get_export_queryset(request)
            # بررسی خالی بودن queryset
            if not queryset:
                messages.error(request, _("No data available to export."))
                return redirect('admin:evaluation_pollassignment_changelist')
            
            export_data = self.get_export_data(file_format, queryset, request=request)
            content_type = file_format.get_content_type()
            response = HttpResponse(export_data, content_type=content_type)
                
            response = queryset[0]
            poll = response.poll_assignment.poll
            if poll:
                print(f'-poll->{poll.name}')
                poll_name_slug = slugify(unidecode(poll.name), allow_unicode=True)  # تبدیل نام Poll به اسلاگ
                print(f'poll_name_slug--> {poll_name_slug}')
                filename = f"{poll_name_slug}.{file_format.get_extension()}"
                print(f'filename--> {filename}')
            else:
                filename = self.get_export_filename(request, queryset, file_format)

            response = HttpResponse(export_data, content_type=content_type)
            response['Content-Disposition'] = f'attachment; filename="{filename}"'

            # response['Content-Disposition'] = 'attachment; filename="%s"' % (
            #     self.get_export_filename(request, queryset, file_format),
            # )

            post_export.send(sender=None, model=self.model)
            return response

        context = self.get_export_context_data()

        context.update(self.admin_site.each_context(request))

        context['title'] = _("Export")
        context['form'] = form
        context['opts'] = self.model._meta
        request.current_app = self.admin_site.name
        return TemplateResponse(request, [self.export_template_name],
                                context)

