import random
import string
from django.db import models
import jdatetime
from django.utils import timezone

from apps.employee.models import Employee



class Poll(models.Model):
    name = models.CharField(max_length=255, verbose_name="Survey Name")
    is_active = models.BooleanField(default=True, verbose_name="Is Active")

    start_date = models.DateField(null=True, blank=True, verbose_name="Start Date")  

    created_at = models.DateTimeField(auto_now_add=True, verbose_name="Created At")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="Updated At")


    class Meta:
        verbose_name = "Survey"
        verbose_name_plural = "Survey"
        indexes = [
            models.Index(fields=['name'], name='poll_name_idx'),
            models.Index(fields=['is_active'], name='poll_is_active_idx'),
        ]
        
    def __str__(self):
        return self.name

    @property
    def jalali_start_date(self):
        if self.start_date:
            jalali_date = jdatetime.date.fromgregorian(date=self.start_date)
            return f"{jalali_date.month:02d}/{jalali_date.year}"
        return None
    
    
class Question(models.Model):
    RESPONSE_TYPES = (
        ('rating', 'Rating(1 to 7)'),
        ('comment', 'Comment'),
        ('both', 'Both'),
    )
    poll = models.ForeignKey(Poll, on_delete=models.CASCADE, related_name="questions", verbose_name="Related Survey")
    text = models.TextField(verbose_name="Question Text")
    category = models.CharField(max_length=100, verbose_name="Question Category")
    description = models.TextField(verbose_name="Description", blank=True, null=True)     
    response_type = models.CharField(max_length=10, choices=RESPONSE_TYPES, default='rating', verbose_name="Response Type")
    question_order = models.PositiveIntegerField(default=0, verbose_name="Question Order")  # ترتیب سوال
        
    is_active = models.BooleanField(default=True, verbose_name="Is Active")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="Created At")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="Updated At")

    class Meta:
        verbose_name = "Question"
        verbose_name_plural = "Questions"
        indexes = [
            models.Index(fields=['text'], name='question_text_idx'),
            models.Index(fields=['category'], name='question_category_idx'),
            models.Index(fields=['is_active'], name='question_is_active_idx'),
        ]
        
    def __str__(self):
        return f"{self.text} ({self.category})"
    
    
    
class PollAssignment(models.Model):
    poll = models.ForeignKey(Poll, on_delete=models.CASCADE, related_name="assignments", verbose_name="Survey")
    assigned_to = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name="assigned_polls", verbose_name="Assigned To")
    access_token = models.CharField(max_length=30, unique=True, verbose_name="Access Token")
    is_active = models.BooleanField(default=True, verbose_name='is active')

    created_at = models.DateTimeField(auto_now_add=True, verbose_name="Created At")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="Updated At")

    class Meta:
        verbose_name = "Poll Assignment"
        verbose_name_plural = "Poll Assignments"
        indexes = [
            models.Index(fields=['poll'], name='poll_idx'),  # Shortened index name
            models.Index(fields=['assigned_to'], name='assigned_to_idx'),  # Shortened index name
            models.Index(fields=['access_token'], name='access_token_idx'),  # Shortened index name
        ]

    def __str__(self):
        return f"Survey {self.poll.name} assigned to {self.assigned_to.fullname}"
    
    def generate_ref_link(self):
        last_digits = str(self.assigned_to.national_code)[-3:]
        random_digits = ''.join(random.choices(string.digits, k=5))
        return f"{random_digits}{last_digits}".lower()
    
    def save(self, *args, **kwargs):
        if not self.access_token:
            self.access_token = self.generate_ref_link()
        super().save(*args, **kwargs)
    
    
class SelectableEmployee(models.Model):
    poll_assignment = models.ForeignKey(PollAssignment, on_delete=models.CASCADE, verbose_name="Survey Assignment", related_name='selectable_employees')
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, verbose_name="Selectable Employee")
    priority = models.PositiveIntegerField(verbose_name="Priority", default=0)

    class Meta:
        verbose_name = "Selectable Employee"
        verbose_name_plural = "Selectable Employees"
        ordering = ['priority']
        indexes = [
            models.Index(fields=['poll_assignment'], name='selectable_employee_poll_assignment_idx'),
            models.Index(fields=['priority'], name='selectable_employee_priority_idx'),
        ]
        
    class Meta:
        ordering = ['priority']  

    def __str__(self):
        return f"{self.employee.fullname} - Priority: {self.priority}"
    
    def save(self, *args, **kwargs):
        if not self.pk:
            count = SelectableEmployee.objects.filter(
                poll_assignment=self.poll_assignment,
                employee=self.employee
            ).count()
            
            self.priority = count + 1
        
        super().save(*args, **kwargs)
    
    
class Response(models.Model):
    poll_assignment = models.ForeignKey(PollAssignment, on_delete=models.CASCADE, related_name="responses", verbose_name="Survey Assignment")
    question = models.ForeignKey(Question, on_delete=models.CASCADE, related_name="responses", verbose_name="Question")

    selected_employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name="responses_received", verbose_name="Selected Employee")
    evaluator = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name="responses_given", verbose_name="Evaluator")  # فیلد جدید

    rating = models.PositiveIntegerField(null=True, blank=True, verbose_name="Rating")
    comment = models.TextField(null=True, blank=True, verbose_name="Comment")

    created_at = models.DateTimeField(auto_now_add=True, verbose_name="Created At")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="Updated At")

    class Meta:
        verbose_name = "Response"
        verbose_name_plural = "Responses"
        indexes = [
            models.Index(fields=['poll_assignment'], name='response_poll_assignment_idx'),
            models.Index(fields=['question'], name='response_question_idx'),
            models.Index(fields=['selected_employee'], name='response_selected_employee_idx'),
        ]
        

    def __str__(self):
        try:
            selected_employee_name = self.selected_employee.fullname
        except Employee.DoesNotExist:
            selected_employee_name = "بدون کارمند انتخاب‌شده"

        try:
            question_text = self.question.text
        except Question.DoesNotExist:
            question_text = "بدون سوال"

        return f"Response for {selected_employee_name} on question {question_text}"