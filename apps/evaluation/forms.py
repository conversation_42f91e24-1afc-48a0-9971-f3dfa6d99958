from django import forms
from django.utils.translation import gettext_lazy as _

from apps.evaluation.models import *


class ImportForm(forms.Form):
    import_file = forms.FileField(label=_('File to import'))
    input_format = forms.ChoiceField(label=_('Format'), choices=())
    poll = forms.ModelChoiceField(
        queryset=Poll.objects.all(),
        label=_('Select Poll'),
        help_text=_('All imported questions will be assigned to this poll.'),
    )

    def __init__(self, import_formats, *args, **kwargs):
        super().__init__(*args, **kwargs)
        choices = []
        for i, f in enumerate(import_formats):
            if f().get_title() == 'xlsx': 
                choices.append((str(i), f().get_title()))
        if len(import_formats) > 1:
            choices.insert(0, ('', '---'))
        self.fields['input_format'].choices = choices
        
    def clean_input_format(self):
        if self.cleaned_data['input_format'] in ['1', '2']:
            return '0'
        return self.cleaned_data['input_format']
    def get_poll_id(self):
        """Returns the poll ID from the form's data."""
        if self.is_valid():
            return self.cleaned_data['poll'].id
        return None


class PollAssignmentExportForm(forms.Form):
    file_format = forms.ChoiceField(
        label=_('Format'),
        choices=(),
        )
    poll = forms.ModelChoiceField(
        queryset=Poll.objects.filter(is_active=True),
        label=_('Select Poll'),
        help_text=_('Only responses related to this poll will be exported.'),
        required=True,  # Ensure the field is required
    )

    def __init__(self, formats, *args, **kwargs):
        super().__init__(*args, **kwargs)
        choices = []
        for i, f in enumerate(formats):
            choices.append((str(i), f().get_title(),))
        if len(formats) > 1:
            choices.insert(0, ('', '---'))

        self.fields['file_format'].choices = choices


    def clean_poll(self):
        poll = self.cleaned_data.get('poll')
        if not poll:
            raise forms.ValidationError(_('Please select a poll.'))
        return poll






class PollAssignmentForm(forms.ModelForm):
    class Meta:
        model = PollAssignment
        fields = '__all__'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['poll'].widget.can_add_related = False
        self.fields['assigned_to'].widget.can_add_related = False

class SelectableEmployeeInlineForm(forms.ModelForm):
    class Meta:
        model = SelectableEmployee
        fields = '__all__' 
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['employee'].widget.can_add_related = False
        
        widgets = {
            'employee': forms.Select(attrs={'class': 'form-control', 'style': 'width: 380px;', 'readonly': 'readonly'}),
            'priority': forms.NumberInput(attrs={'class': 'form-control', 'min': 0,'width': "10px"}),
        }


class ResponseExportForm(forms.Form):
    GROUPING_CHOICES = [
        ('evaluatee', 'گروه بندی بر اساس ارزیابی شونده'),
        ('evaluator', 'گروه بندی بر اساس ارزیابی کننده'),
    ]

    file_format = forms.ChoiceField(
        label=_('Format'),
        choices=(),
        )
    poll = forms.ModelChoiceField(
        queryset=Poll.objects.all(),
        label=_('Select Poll'),
        help_text=_('Only responses related to this poll will be exported.'),
        required=True,  # Ensure the field is required
    )
    grouping_type = forms.ChoiceField(
        label=_('نوع گروه بندی'),
        choices=GROUPING_CHOICES,
        initial='evaluatee',
        required=True,
    )
    def __init__(self, formats, *args, **kwargs):
        super().__init__(*args, **kwargs)
        choices = []
        for i, f in enumerate(formats):
            choices.append((str(i), f().get_title(),))
        if len(formats) > 1:
            choices.insert(0, ('', '---'))

        self.fields['file_format'].choices = choices


    def clean_poll(self):
        poll = self.cleaned_data.get('poll')
        if not poll:
            raise forms.ValidationError(_('Please select a poll.'))
        return poll


class ResponseForm(forms.ModelForm):
    class Meta:
        model = Response
        fields = '__all__'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['poll_assignment'].widget.can_add_related = False
        self.fields['question'].widget.can_add_related = False
        self.fields['selected_employee'].widget.can_add_related = False
        self.fields['evaluator'].widget.can_add_related = False
