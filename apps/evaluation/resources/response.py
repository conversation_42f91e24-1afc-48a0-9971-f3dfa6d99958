from django import forms
from django.contrib import admin
from import_export.forms import ImportForm
from django.utils.translation import gettext_lazy as _

from apps.evaluation.models import *
from apps.employee.models import Employee

from import_export.widgets import ForeignKeyWidget
from django.core.exceptions import ObjectDoesNotExist
from django.core.exceptions import PermissionDenied

from import_export.resources import ModelResource
from import_export import fields, widgets




class ResponseResource(ModelResource):
    question = fields.Field(column_name='Question', attribute='question__text')
    evaluator_fullname = fields.Field(column_name='Evaluator Fullname', attribute='evaluator__fullname')
    evaluator_national_code = fields.Field(column_name='Evaluator National Code', attribute='evaluator__national_code')
    evaluatee_fullname = fields.Field(column_name='Evaluatee Fullname', attribute='selected_employee__fullname')
    evaluatee_national_code = fields.Field(column_name='Evaluatee National Code', attribute='selected_employee__national_code')
    response = fields.Field(column_name='Response')
    response_created_at = fields.Field(column_name='Response Date')
    poll_start_month = fields.Field(column_name='Month')
    poll_start_year = fields.Field(column_name='Year')
    poll_name = fields.Field(column_name='Survey', attribute='poll_assignment__poll__name')
    
    class Meta:
        model = Response
        fields = (
            'question',
            'evaluatee_fullname',
            'evaluatee_national_code',
            'evaluator_fullname',
            'evaluator_national_code',
            'response',
            'poll_start_month',
            'poll_start_year',
            'response_created_at',
            'poll_name'
        )
        export_order = (
            'question',
            'evaluatee_fullname',
            'evaluatee_national_code',
            'evaluator_fullname',
            'evaluator_national_code',
            'response',
            'poll_start_month',
            'poll_start_year',
            'response_created_at',
            'poll_name'
        )

    def dehydrate_question(self, response):
        return f"{response.question.question_order}-{response.question.text}"

    def dehydrate_response(self, response):
        if response.rating is not None:
            return str(response.rating)
        elif response.comment:
            return response.comment
        else:
            return "بدون پاسخ"

    def dehydrate_question(self, response):
        if response.question:
            return f"{response.question.question_order}-{response.question.text}"
        return "بدون سوال"

    def dehydrate_evaluatee_fullname(self, response):
        """
        اگر selected_employee وجود نداشت، "شرکت نکرده است" نمایش داده شود.
        """
        try:
            if response.selected_employee:
                return response.selected_employee.fullname
        except Employee.DoesNotExist:
            pass
        return "شرکت نکرده است"
    
    def dehydrate_evaluatee_national_code(self, response):
        """
        اگر selected_employee وجود نداشت، "شرکت نکرده است" نمایش داده شود.
        """
        try:
            if response.selected_employee:
                return response.selected_employee.national_code
        except Employee.DoesNotExist:
            pass
        return "شرکت نکرده است"    
    def dehydrate_poll_start_month(self, response):
        """
        ماه شروع Poll به صورت جلالی (مثلاً 09).
        """
        poll = response.poll_assignment.poll
        if poll.start_date:
            jalali_date = jdatetime.date.fromgregorian(date=poll.start_date)
            return f"{jalali_date.month:02d}"
        return "نامشخص"

    def dehydrate_poll_start_year(self, response):
        """
        سال شروع Poll به صورت جلالی (مثلاً 1404).
        """
        poll = response.poll_assignment.poll
        if poll.start_date:
            jalali_date = jdatetime.date.fromgregorian(date=poll.start_date)
            return str(jalali_date.year)
        return "نامشخص"

    def dehydrate_response_created_at(self, response):
        """
        تاریخ پاسخ (created_at) به صورت جلالی (مثلاً 1404/02/03).
        """
        if response.created_at:
            jalali_date = jdatetime.datetime.fromgregorian(datetime=response.created_at)
            return jalali_date.strftime("%Y/%m/%d")
        return "نامشخص"

    def get_export_queryset(self, request):
        print(f'-1-get_export_queryset-> ================================')
        queryset = super().get_export_queryset(request)
        poll_id = request.POST.get('poll')  # شناسه Poll انتخاب‌شده از فرم
        if not poll_id:
            return Response.objects.none()
        print(f'-2-get_export_queryset-> {poll_id}')

        queryset = queryset.filter(poll_assignment__poll_id=poll_id)

        poll_assignments_without_responses = PollAssignment.objects.filter(
            poll_id=poll_id,
            responses__isnull=True
        )

        print(f'-3-get_export_queryset-> {poll_assignments_without_responses}')
        export_data = []
        export_data.extend(assignment.responses.all())
        for assignment in poll_assignments_without_responses:
            fake_response = Response(
                poll_assignment=assignment,
                question=assignment.poll.questions.first(),  # اولین سوال Poll
                evaluator=assignment.assigned_to,  # Evaluator همان assigned_to است
                selected_employee=None,  # چون هیچ Response وجود ندارد
                rating=None,
                comment=None
            )
            export_data.append(fake_response)

        return export_data    