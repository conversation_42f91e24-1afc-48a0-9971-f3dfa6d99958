from django import forms
from django.contrib import admin
from import_export.forms import ImportForm
from django.utils.translation import gettext_lazy as _

from apps.evaluation.models import *
from apps.employee.models import Employee

from import_export.widgets import ForeignKeyWidget
from django.core.exceptions import ObjectDoesNotExist
from django.core.exceptions import PermissionDenied
from config.settings import base as settings
from import_export.resources import ModelResource
from import_export import fields, widgets


class PollAssignmentResource(ModelResource):
    evaluator_national_code = fields.Field(
        column_name='evaluator_national_code',
        attribute='assigned_to',
        widget=ForeignKeyWidget(Employee, 'national_code'),
        readonly=False,
    )
    evaluatee_national_code = fields.Field(
        column_name='evaluatee_national_code',
        attribute='selectable_employees',
        readonly=False,
        widget=widgets.ManyToManyWidget(SelectableEmployee, field='employee')
    )

    employee_fullname = fields.Field(column_name='Fullname', attribute='assigned_to__fullname', readonly=True)
    employee_national_code = fields.Field(column_name='National Code', attribute='assigned_to__national_code', readonly=True)
    employee_phone_number = fields.Field(column_name='PhoneNumber', attribute='assigned_to__phone_number', readonly=True)
    
    poll_name = fields.Field(column_name='Poll', attribute='poll__name', readonly=True)    
    access_token = fields.Field(column_name='Link', readonly=True)
    

    class Meta:
        model = PollAssignment
        fields = ('evaluator_national_code', 'evaluatee_national_code', )
        import_id_fields = ('evaluator_national_code', 'evaluatee_national_code')

    def dehydrate_access_token(self, poll_assignment):
        return f"{settings.DOMAIN_POLL}{poll_assignment.access_token}"
    
    def get_export_queryset(self, request):
        print(f'-1-get_export_queryset-> ================================')
        queryset = super().get_export_queryset(request)
        poll_id = request.POST.get('poll')  # شناسه Poll انتخاب‌شده از فرم
        if not poll_id:
            return PollAssignmentResource.objects.none()
        print(f'-2-get_export_queryset-> {poll_id}')

        queryset = queryset.filter(poll__id=poll_id, is_active=True, assigned_to__is_evaluator=True, assigned_to__is_active=True)
        return queryset

    def get_import_fields(self):
        return [
            self.fields['evaluator_national_code'],
            self.fields['evaluatee_national_code'],
        ]
        
    def get_export_fields(self):
        return [
            self.fields['employee_national_code'],
            self.fields['employee_fullname'],
            self.fields['employee_phone_number'],
            self.fields['poll_name'],
            self.fields['access_token'],

        ]
        
        
    def before_import_row(self, row, **kwargs):
        if not row.get('evaluator_national_code') or not row.get('evaluatee_national_code'):
            raise ValueError("Row is missing required columns.")

    def before_import(self, dataset, using_transactions, dry_run, **kwargs):
        self.dataset = dataset
        self.poll = Poll.objects.get(id=kwargs.get('poll'))  # Use the selected Poll
        super().before_import(dataset, using_transactions, dry_run, **kwargs)


    def get_or_create_employee(self, national_code, fullname, is_evaluator=False):
        try:
            employee, created = Employee.objects.get_or_create(
                national_code=national_code,
                defaults={'fullname': fullname, 'is_evaluator': is_evaluator}
            )
            if not created:
                # Update the existing record if necessary
                employee.fullname = fullname
            employee.is_evaluator = True if employee.is_evaluator else is_evaluator
            employee.save()
            return employee
        except Exception:
            # Handle the case where the record already exists
            employee = Employee.objects.get(national_code=national_code)
            employee.is_evaluator = True if employee.is_evaluator else is_evaluator
            employee.save()
            return employee


    def before_save_instance(self, instance, using_transactions, dry_run):
        if not dry_run:
            print(f'========before_save_instance========')
            instance.poll = self.poll
            instance.save()

            evaluator_nc = instance.assigned_to.national_code
            evaluatees = [
                (row['evaluatee_national_code'], row.get('evaluatee_fullname', ''))
                for row in self.dataset.dict
                if row['evaluator_national_code'] == evaluator_nc
            ]
            selectable_employees = []
            for priority, (nc, name) in enumerate(evaluatees, start=1):
                employee = self.get_or_create_employee(nc, name)
                selected, _ = SelectableEmployee.objects.get_or_create(
                    poll_assignment=instance,
                    employee=employee,
                    # priority=priority                    
                )
                                
                print(f'--> evaluator_nc:{evaluator_nc}// => {employee}// selected: {selected} selected_id: {selected.id}')                

            selectable_employees = SelectableEmployee.objects.filter(poll_assignment=instance)
            employee_names = [employee.employee for employee in selectable_employees]

            instance.save()
        super().before_save_instance(instance, using_transactions, dry_run)


    def get_or_init_instance(self, instance_loader, row):
        try:
            evaluator = self.get_or_create_employee(
                row['evaluator_national_code'],
                row.get('evaluator_fullname', ''),
                is_evaluator=True
            )
            print(f'-1--get_or_init_instance--> {evaluator}')
            instance, created = PollAssignment.objects.get_or_create(
                assigned_to=evaluator,
                poll=self.poll,
            )
            print(f'-1-1-get_or_init_instance--> {instance}// {created}')
            return instance, created
        except Exception as e:
            raise ValueError(f"Error initializing instance: {e}")
