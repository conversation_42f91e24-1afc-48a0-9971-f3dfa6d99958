from django import forms
from django.contrib import admin
from import_export.forms import ImportForm
from django.utils.translation import gettext_lazy as _

from apps.evaluation.models import *
from apps.employee.models import Employee

from import_export.widgets import ForeignKeyWidget
from django.core.exceptions import ObjectDoesNotExist
from django.core.exceptions import PermissionDenied

from import_export.resources import ModelResource
from import_export import fields, widgets

    
class QuestionResource(ModelResource):
    text = fields.Field(attribute='text', column_name='question')
    category = fields.Field(attribute='category', column_name='category')  
    response_type = fields.Field(attribute='response_type', column_name='type')  
    # description = fields.Field(attribute='description', column_name='description', default=None, saves_null_values=True)
        
    class Meta:
        skip_diff = True
        model = Question
        fields = ('text', 'category', 'response_type')
        import_id_fields = ('text',) 
        exclude = ('id',)

    def before_import_row(self, row, **kwargs):        
        if not row.get('question') or not row.get('category') or not row.get('type'):
            raise ValueError("ردیف فاقد ستون‌های مورد نیاز است.")
                    
    def before_import(self, dataset, using_transactions, dry_run, **kwargs):
        poll = Poll.objects.get(id=int(kwargs.get('poll')))
        self.last_poll = poll
        super().before_import(dataset, using_transactions, dry_run, **kwargs)

    def before_save_instance(self, instance, using_transactions, dry_run):
        instance.poll = self.last_poll
        
        if not dry_run:
            instance.question_order = self.get_next_question_order()
        
        super().before_save_instance(instance, using_transactions, dry_run)

    def get_next_question_order(self):
        last_question = Question.objects.filter(poll=self.last_poll).order_by('-question_order').first()
        if last_question:
            return last_question.question_order + 1
        return 0        

