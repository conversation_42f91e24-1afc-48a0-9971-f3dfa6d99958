
from rest_framework import serializers, views
from rest_framework.response import Response
from rest_framework import status

from apps.evaluation.models import *
from apps.employee.models import *



class PollInfoSerializer(serializers.ModelSerializer):
    employee_fullname = serializers.CharField(source='assigned_to.fullname', read_only=True)
    poll_name = serializers.CharField(source='poll.name', read_only=True)
    questions = serializers.SerializerMethodField()
    responses_count = serializers.SerializerMethodField()
    questions_count = serializers.SerializerMethodField()
    
    class Meta:
        model = PollAssignment
        fields = ['employee_fullname', 'poll_name', 'questions', 'questions_count', 'responses_count']

    def get_questions(self, obj):
        questions = Question.objects.filter(poll=obj.poll).order_by('question_order')
        return [question.id for question in questions]

    def get_questions_count(self, obj):
        return Question.objects.filter(poll=obj.poll).count()

    def get_responses_count(self, obj):
        evaluator = obj.assigned_to
        # دریافت همه سوالات فعال مربوط به این نظرسنجی
        questions = Question.objects.filter(poll=obj.poll, is_active=True).order_by('question_order')
        # دریافت تعداد کارمندانی که باید توسط این کارمند ارزیابی شوند
        total_employees_to_evaluate = SelectableEmployee.objects.filter(poll_assignment=obj).count()
        
        # بررسی هر سوال به ترتیب
        for question in questions:
            # تعداد پاسخ‌های ثبت‌شده برای این سوال توسط کارمند ارزیابی‌کننده
            total_responses_for_question = Response.objects.filter(
                poll_assignment=obj,
                evaluator=evaluator,
                question=question
            ).count()
            print(f'---> {question.question_order}//total_employees_to_evaluate:{total_employees_to_evaluate} //total_responses_for_question:{total_responses_for_question}')
            # # اگر تعداد پاسخ‌ها کمتر از تعداد کارمندهای ارزیابی‌شونده باشد، این سوال کامل پاسخ داده نشده است
            # if total_responses_for_question <= 1:
            #     return question.question_order

            # اگر کاربر برای این سوال هیچ پاسخی ثبت نکرده باشد
            if total_responses_for_question == 0:
                print(f'--1->=')
                return question.question_order
            
            # اگر کاربر برای این سوال به‌طور کامل پاسخ داده باشد، به سوال بعدی برو
            if total_responses_for_question >= total_employees_to_evaluate:
                print(f'--2->============')
                continue

            # اگر کاربر برای این سوال حداقل یک پاسخ ثبت کرده باشد، اما هنوز به‌طور کامل پاسخ نداده باشد
            if 0 < total_responses_for_question < total_employees_to_evaluate:
                print(f'--3->==')
                continue
                # return question.question_order

            
        return questions.count()
        

class SelectableEmployeeSerializer(serializers.ModelSerializer):
    employee_name = serializers.CharField(source='employee.fullname')
    employee_id = serializers.IntegerField(source='employee.id')
    previous_response = serializers.SerializerMethodField()

    class Meta:
        model = SelectableEmployee
        fields = ['employee_id', 'employee_name', 'previous_response']

    def get_previous_response(self, obj):
        evaluator = self.context.get('evaluator')
        question = self.context.get('question')
        try:
            response = Response.objects.get(
                poll_assignment__assigned_to=evaluator,
                question=question,
                selected_employee=obj.employee
            )
            return {
                'rating': response.rating,
                'comment': response.comment
            }
        except Response.DoesNotExist:
            return {
                'rating': None,
                'comment': None,
            }
        

class QuestionDetailSerializer(serializers.ModelSerializer):
    selectable_employees = serializers.SerializerMethodField()

    class Meta:
        model = Question
        fields = ['id', 'text', 'description', 'category', 'response_type', 'question_order', 'selectable_employees']

    def get_selectable_employees(self, obj):
        evaluator = self.context.get('evaluator')
        poll_assignment = self.context.get('poll_assignment')
        selectable_employees = SelectableEmployee.objects.filter(poll_assignment=poll_assignment)
        return SelectableEmployeeSerializer(
            selectable_employees,
            many=True,
            context={'evaluator': evaluator, 'question': obj}
        ).data
        

class SelectedEmployeeSerializer(serializers.Serializer):
    id = serializers.IntegerField(required=True)
    rating = serializers.IntegerField(min_value=1, max_value=7, required=False, allow_null=True)  #
    comment = serializers.CharField(allow_blank=True, required=False, allow_null=True)  # اختیاری و nullable

class SubmitResponsesSerializer(serializers.Serializer):
    access_token = serializers.CharField(max_length=30, required=True)
    question_id = serializers.IntegerField(required=True)
    selected_employees = SelectedEmployeeSerializer(many=True, required=True)

    def validate_access_token(self, value):
        """Validate that the access token exists and is unique."""
        try:
            self.poll_assignment = PollAssignment.objects.get(access_token=value)
        except PollAssignment.DoesNotExist:
            raise serializers.ValidationError("Invalid access token.")
        return value

    def validate_question_id(self, value):
        """Validate that the question belongs to the poll in the poll assignment."""
        try:
            self.question = Question.objects.get(id=value)
            if self.question.poll != self.poll_assignment.poll:
                raise serializers.ValidationError("Question does not belong to the assigned poll.")
        except Question.DoesNotExist:
            raise serializers.ValidationError("Invalid question ID.")
        return value

    def validate_selected_employees(self, value):
        """Validate that all selected employees are selectable in the poll assignment."""
        valid_employee_ids = set(
            self.poll_assignment.selectable_employees.values_list('employee__id', flat=True)
        )
        invalid_employees = [emp['id'] for emp in value if emp['id'] not in valid_employee_ids]
        if invalid_employees:
            raise serializers.ValidationError(f"Invalid employee IDs: {invalid_employees}.")
        return value