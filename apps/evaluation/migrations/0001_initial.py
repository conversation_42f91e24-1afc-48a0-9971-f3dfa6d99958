# Generated by Django 3.2.25 on 2025-02-02 11:31

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('employee', '0002_auto_20250202_0101'),
    ]

    operations = [
        migrations.CreateModel(
            name='Poll',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, verbose_name='Survey Name')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
            ],
        ),
        migrations.CreateModel(
            name='PollAssignment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('access_token', models.CharField(editable=False, max_length=10, unique=True, verbose_name='Access Token')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('assigned_to', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assigned_polls', to='employee.employee', verbose_name='Assigned To')),
                ('poll', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assignments', to='evaluation.poll', verbose_name='Survey')),
            ],
        ),
        migrations.CreateModel(
            name='Question',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('text', models.TextField(verbose_name='Question Text')),
                ('category', models.CharField(max_length=100, verbose_name='Question Category')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('poll', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='questions', to='evaluation.poll', verbose_name='Related Survey')),
            ],
        ),
        migrations.CreateModel(
            name='SelectableEmployee',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('priority', models.PositiveIntegerField(verbose_name='Priority')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='employee.employee', verbose_name='Selectable Employee')),
                ('poll_assignment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='evaluation.pollassignment', verbose_name='Survey Assignment')),
            ],
            options={
                'ordering': ['priority'],
            },
        ),
        migrations.CreateModel(
            name='Response',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('response_type', models.CharField(choices=[('rating', 'Rating(1 to 7)'), ('comment', 'Comment'), ('both', 'Both')], default='rating', max_length=10, verbose_name='Response Type')),
                ('rating', models.PositiveIntegerField(blank=True, null=True, verbose_name='Rating')),
                ('comment', models.TextField(blank=True, null=True, verbose_name='Comment')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('poll_assignment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='responses', to='evaluation.pollassignment', verbose_name='Survey Assignment')),
                ('question', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='responses', to='evaluation.question', verbose_name='Question')),
                ('selected_employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='responses_received', to='employee.employee', verbose_name='Selected Employee')),
            ],
        ),
        migrations.AddField(
            model_name='pollassignment',
            name='selectable_employees',
            field=models.ManyToManyField(related_name='selectable_in_polls', through='evaluation.SelectableEmployee', to='employee.Employee', verbose_name='Selectable Employees'),
        ),
    ]
