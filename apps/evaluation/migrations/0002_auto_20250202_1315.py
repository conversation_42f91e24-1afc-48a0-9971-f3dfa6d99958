# Generated by Django 3.2.25 on 2025-02-02 13:15

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('employee', '0002_auto_20250202_0101'),
        ('evaluation', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='poll',
            options={'verbose_name': 'Survey', 'verbose_name_plural': 'Survey'},
        ),
        migrations.AlterModelOptions(
            name='pollassignment',
            options={'verbose_name': 'Poll Assignment', 'verbose_name_plural': 'Poll Assignments'},
        ),
        migrations.AlterModelOptions(
            name='question',
            options={'verbose_name': 'Question', 'verbose_name_plural': 'Questions'},
        ),
        migrations.AlterModelOptions(
            name='response',
            options={'verbose_name': 'Response', 'verbose_name_plural': 'Responses'},
        ),
        migrations.RemoveField(
            model_name='response',
            name='response_type',
        ),
        migrations.AddField(
            model_name='question',
            name='question_order',
            field=models.PositiveIntegerField(default=0, verbose_name='Question Order'),
        ),
        migrations.AddField(
            model_name='question',
            name='response_type',
            field=models.CharField(choices=[('rating', 'Rating(1 to 7)'), ('comment', 'Comment'), ('both', 'Both')], default='rating', max_length=10, verbose_name='Response Type'),
        ),
        migrations.AddField(
            model_name='response',
            name='evaluator',
            field=models.ForeignKey(default='1', on_delete=django.db.models.deletion.CASCADE, related_name='responses_given', to='employee.employee', verbose_name='Evaluator'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='pollassignment',
            name='access_token',
            field=models.CharField(max_length=30, unique=True, verbose_name='Access Token'),
        ),
        migrations.AddIndex(
            model_name='poll',
            index=models.Index(fields=['name'], name='poll_name_idx'),
        ),
        migrations.AddIndex(
            model_name='poll',
            index=models.Index(fields=['is_active'], name='poll_is_active_idx'),
        ),
        migrations.AddIndex(
            model_name='pollassignment',
            index=models.Index(fields=['poll'], name='poll_idx'),
        ),
        migrations.AddIndex(
            model_name='pollassignment',
            index=models.Index(fields=['assigned_to'], name='assigned_to_idx'),
        ),
        migrations.AddIndex(
            model_name='pollassignment',
            index=models.Index(fields=['access_token'], name='access_token_idx'),
        ),
        migrations.AddIndex(
            model_name='question',
            index=models.Index(fields=['text'], name='question_text_idx'),
        ),
        migrations.AddIndex(
            model_name='question',
            index=models.Index(fields=['category'], name='question_category_idx'),
        ),
        migrations.AddIndex(
            model_name='question',
            index=models.Index(fields=['is_active'], name='question_is_active_idx'),
        ),
        migrations.AddIndex(
            model_name='response',
            index=models.Index(fields=['poll_assignment'], name='response_poll_assignment_idx'),
        ),
        migrations.AddIndex(
            model_name='response',
            index=models.Index(fields=['question'], name='response_question_idx'),
        ),
        migrations.AddIndex(
            model_name='response',
            index=models.Index(fields=['selected_employee'], name='response_selected_employee_idx'),
        ),
    ]
