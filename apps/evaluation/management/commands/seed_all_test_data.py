from django.core.management.base import BaseCommand
from django.core.management import call_command
from faker import Faker


class Command(BaseCommand):
    help = 'Generate comprehensive test Excel files for import testing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--employees',
            type=int,
            default=1000,
            help='Number of employees to generate (default: 1000)'
        )
        parser.add_argument(
            '--assignments',
            type=int,
            default=1000,
            help='Number of poll assignment records to generate (default: 1000)'
        )
        parser.add_argument(
            '--evaluators',
            type=int,
            default=200,
            help='Number of unique evaluators for assignments (default: 200)'
        )
        parser.add_argument(
            '--evaluatees',
            type=int,
            default=500,
            help='Number of unique evaluatees for assignments (default: 500)'
        )
        parser.add_argument(
            '--output-dir',
            type=str,
            default='test_data',
            help='Output directory for Excel files (default: test_data)'
        )

    def handle(self, *args, **options):
        employees_count = options['employees']
        assignments_count = options['assignments']
        evaluators_count = options['evaluators']
        evaluatees_count = options['evaluatees']
        output_dir = options['output_dir']
        
        fake = Faker('fa_IR')

        self.stdout.write(
            self.style.SUCCESS(
                f'🎯 Starting Excel files generation...'
                f'\n- Employees Excel: {employees_count} records'
                f'\n- Assignments Excel: {assignments_count} records'
                f'\n- Unique evaluators: {evaluators_count}'
                f'\n- Unique evaluatees: {evaluatees_count}'
                f'\n- Output directory: {output_dir}/'
            )
        )

        try:
            # مرحله 1: ایجاد فایل Excel کارمندان
            self.stdout.write(
                self.style.SUCCESS('\n=== Step 1: Generating Employees Excel File ===')
            )
            employees_filename = f'{output_dir}/employees_{employees_count}.xlsx'
            call_command(
                'seed_employees',
                count=employees_count,
                output=employees_filename,
                verbosity=1
            )

            # مرحله 2: ایجاد فایل Excel assignments
            self.stdout.write(
                self.style.SUCCESS('\n=== Step 2: Generating Poll Assignments Excel File ===')
            )
            assignments_filename = f'{output_dir}/poll_assignments_{assignments_count}.xlsx'
            call_command(
                'seed_poll_assignments',
                count=assignments_count,
                output=assignments_filename,
                evaluators=evaluators_count,
                evaluatees=evaluatees_count,
                verbosity=1
            )

            # مرحله 3: ایجاد فایل Excel تستی برای import (استفاده از generate_test_excel)
            self.stdout.write(
                self.style.SUCCESS('\n=== Step 3: Generating Import Test Excel File ===')
            )
            import_test_filename = f'{output_dir}/import_test_{assignments_count}.xlsx'
            call_command(
                'generate_test_excel',
                count=assignments_count,
                output=import_test_filename,
                verbosity=1
            )

            self.stdout.write(
                self.style.SUCCESS('\n✅ All Excel files generated successfully!')
            )

            # نمایش خلاصه نهایی
            self.stdout.write(
                self.style.SUCCESS(
                    f'\n📊 Generated Files Summary:'
                    f'\n{"="*60}'
                    f'\n📋 {employees_filename}'
                    f'\n   └── {employees_count} employee records'
                    f'\n📋 {assignments_filename}'
                    f'\n   └── {assignments_count} assignment records'
                    f'\n   └── {evaluators_count} unique evaluators'
                    f'\n   └── {evaluatees_count} unique evaluatees'
                    f'\n📋 {import_test_filename}'
                    f'\n   └── {assignments_count} test records for import'
                    f'\n{"="*60}'
                    f'\n'
                )
            )

            # نمایش راهنمای استفاده
            self.stdout.write(
                self.style.SUCCESS(
                    f'🚀 How to use these files:'
                    f'\n'
                    f'\n1. Employee Import:'
                    f'\n   - Use {employees_filename} to import employees to database'
                    f'\n'
                    f'\n2. Poll Assignment Import:'
                    f'\n   - Use {assignments_filename} or {import_test_filename}'
                    f'\n   - Go to Admin Panel > Evaluation > Poll Assignments > Import'
                    f'\n   - Select your Poll and upload the Excel file'
                    f'\n   - The Celery task will process it in background'
                    f'\n'
                    f'\n3. Testing large imports:'
                    f'\n   - These files are perfect for testing timeout issues'
                    f'\n   - Monitor the import progress in Admin > Import Tasks'
                    f'\n'
                )
            )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Error during Excel generation: {str(e)}')
            )
            raise