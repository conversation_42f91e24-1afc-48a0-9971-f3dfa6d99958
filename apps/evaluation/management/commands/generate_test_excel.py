import random
import pandas as pd
from django.core.management.base import BaseCommand
from faker import Faker


class Command(BaseCommand):
    help = 'Generate test Excel file with fake assignment data (no database dependency)'

    def add_arguments(self, parser):
        parser.add_argument(
            '--count',
            type=int,
            default=1000,
            help='Number of assignment records to generate (default: 1000)'
        )
        parser.add_argument(
            '--output',
            type=str,
            default='test_assignments_1000.xlsx',
            help='Output Excel file name (default: test_assignments_1000.xlsx)'
        )
        parser.add_argument(
            '--evaluators',
            type=int,
            default=150,
            help='Number of unique evaluators (default: 150)'
        )
        parser.add_argument(
            '--evaluatees',
            type=int,
            default=400,
            help='Number of unique evaluatees (default: 400)'
        )

    def handle(self, *args, **options):
        count = options['count']
        output_file = options['output']
        num_evaluators = options['evaluators']
        num_evaluatees = options['evaluatees']
        
        fake = Faker('fa_IR')
        
        # لیست شرکت‌ها برای تنوع
        companies = [
            'داده پرداز افق نو',
            'فناوری اطلاعات پارس', 
            'صنایع نرم‌افزاری ایران',
            'شرکت توسعه فناوری',
            'مهندسی نرم‌افزار آسیا',
            'فناوری‌های نوین دیجیتال',
            'سیستم‌های هوشمند ایران',
            'شرکت نوآوری تکنولوژی'
        ]

        self.stdout.write(
            self.style.SUCCESS(f'Generating {count} fake assignment records...')
        )

        # ایجاد pool evaluators
        evaluators = []
        used_national_codes = set()
        
        self.stdout.write(
            self.style.SUCCESS(f'Creating {num_evaluators} unique evaluators...')
        )
        
        for i in range(num_evaluators):
            while True:
                national_code = fake.ssn()
                if national_code not in used_national_codes:
                    used_national_codes.add(national_code)
                    break
                    
            evaluators.append({
                'national_code': national_code,
                'fullname': fake.name(),
                'company': random.choice(companies)
            })

        # ایجاد pool evaluatees
        evaluatees = []
        
        self.stdout.write(
            self.style.SUCCESS(f'Creating {num_evaluatees} unique evaluatees...')
        )
        
        for i in range(num_evaluatees):
            while True:
                national_code = fake.ssn()
                if national_code not in used_national_codes:
                    used_national_codes.add(national_code)
                    break
                    
            evaluatees.append({
                'national_code': national_code,
                'fullname': fake.name(),
                'company': random.choice(companies)
            })

        # ایجاد assignment records
        assignment_data = []
        
        self.stdout.write(
            self.style.SUCCESS(f'Generating {count} assignment records...')
        )
        
        for i in range(count):
            evaluator = random.choice(evaluators)
            evaluatee = random.choice(evaluatees)
            
            # اطمینان از اینکه evaluator و evaluatee متفاوت باشند
            while evaluator['national_code'] == evaluatee['national_code']:
                evaluatee = random.choice(evaluatees)
            
            assignment_data.append({
                'evaluator_national_code': evaluator['national_code'],
                'evaluator_fullname': evaluator['fullname'],
                'evaluatee_national_code': evaluatee['national_code'],
                'evaluatee_fullname': evaluatee['fullname']
            })
            
            # نمایش پیشرفت
            if (i + 1) % 200 == 0:
                self.stdout.write(
                    self.style.SUCCESS(f'Generated {i + 1}/{count} records...')
                )

        # ذخیره در Excel
        df = pd.DataFrame(assignment_data)
        
        try:
            df.to_excel(output_file, index=False, engine='openpyxl')
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully generated Excel file: {output_file}'
                    f'\n- Total records: {len(df)}'
                    f'\n- Unique evaluators: {df["evaluator_national_code"].nunique()}'
                    f'\n- Unique evaluatees: {df["evaluatee_national_code"].nunique()}'
                    f'\n- Total unique people: {len(set(df["evaluator_national_code"].tolist() + df["evaluatee_national_code"].tolist()))}'
                )
            )
            
            # نمایش نمونه داده‌ها
            self.stdout.write(
                self.style.SUCCESS('\nSample data (first 3 rows):')
            )
            self.stdout.write(str(df.head(3)))
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error generating Excel file: {str(e)}')
            )