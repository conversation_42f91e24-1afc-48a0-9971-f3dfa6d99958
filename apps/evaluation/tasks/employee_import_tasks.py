import os
from celery import shared_task
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from import_export import formats
from import_export.formats import base_formats
from django.core.exceptions import ObjectDoesNotExist
from django.utils.encoding import force_str

from apps.employee.models import Employee
from apps.employee.resources import EmployeedResource


@shared_task(bind=True)
def process_employee_import_file(self, file_path, user_id=None):
    """
    تسک Celery برای پردازش فایل import کارمندان
    
    Args:
        file_path: مسیر فایل ذخیره شده
        user_id: شناسه کاربر (اختیاری)
    """
    try:
        print(f'--- Employee Import Task Started ----<----')
        # خواندن فایل از مسیر ذخیره شده
        if not default_storage.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
        
        with default_storage.open(file_path, 'rb') as file:
            file_content = file.read()
        
        # تشخیص فرمت فایل از روی پسوند
        file_extension = os.path.splitext(file_path)[1].lower()
        
        if file_extension == '.xlsx':
            input_format = base_formats.XLSX()
        elif file_extension == '.xls':
            input_format = base_formats.XLS()
        elif file_extension == '.csv':
            input_format = base_formats.CSV()
        else:
            raise ValueError(f"Unsupported file format: {file_extension}")
        
        # ایجاد dataset از فایل
        if not input_format.is_binary():
            file_content = force_str(file_content, 'utf-8')
        
        dataset = input_format.create_dataset(file_content)
        
        # ایجاد resource instance و import data
        resource = EmployeedResource()
        
        # شروع import process
        result = resource.import_data(
            dataset, 
            dry_run=False,
            raise_errors=True
        )
        
        # بازگرداندن نتیجه
        print(f'--- Employee Import Task Completed ----<----')
        return {
            'success': True,
            'message': f'Successfully imported {len(dataset)} records',
            'total_new': result.totals.get('new', 0),
            'total_update': result.totals.get('update', 0),
            'total_delete': result.totals.get('delete', 0),
            'total_skip': result.totals.get('skip', 0),
            'has_errors': result.has_errors(),
            'errors': [str(error.error) for error in result.base_errors] if result.has_errors() else []
        }
        
    except Exception as e:
        # ثبت خطا و بازگرداندن
        return {
            'success': False,
            'message': str(e),
            'error_type': type(e).__name__
        }


@shared_task
def cleanup_temp_employee_import_files():
    """
    تسک برای پاک کردن فایل‌های موقت import کارمندان که قدیمی شده‌اند
    """
    import datetime
    from django.utils import timezone
    
    # پاک کردن فایل‌هایی که بیش از 24 ساعت قدیمت دارند
    cutoff_time = timezone.now() - datetime.timedelta(hours=24)
    
    # این بخش نیاز به پیاده سازی logic پاک کردن فایل های قدیمی دارد
    # فعلاً فقط یک placeholder است
    return "Employee import cleanup completed"