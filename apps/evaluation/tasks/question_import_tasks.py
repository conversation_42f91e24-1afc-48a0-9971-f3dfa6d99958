import os
from celery import shared_task
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from import_export import formats
from import_export.formats import base_formats
from django.core.exceptions import ObjectDoesNotExist
from django.utils.encoding import force_str

from apps.evaluation.models import *
from apps.evaluation.resources.question import QuestionResource


@shared_task(bind=True)
def process_question_import_file(self, file_path, poll_id, user_id=None):
    """
    تسک Celery برای پردازش فایل import سوالات
    
    Args:
        file_path: مسیر فایل ذخیره شده
        poll_id: شناسه Poll
        user_id: شناسه کاربر (اختیاری)
    """
    try:
        print(f'--- Question Import Task Started ----<----')
        # خواندن فایل از مسیر ذخیره شده
        if not default_storage.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
        
        with default_storage.open(file_path, 'rb') as file:
            file_content = file.read()
        
        # تشخیص فرمت فایل از روی پسوند
        file_extension = os.path.splitext(file_path)[1].lower()
        
        if file_extension == '.xlsx':
            input_format = base_formats.XLSX()
        elif file_extension == '.xls':
            input_format = base_formats.XLS()
        elif file_extension == '.csv':
            input_format = base_formats.CSV()
        else:
            raise ValueError(f"Unsupported file format: {file_extension}")
        
        # ایجاد dataset از فایل
        if not input_format.is_binary():
            file_content = force_str(file_content, 'utf-8')
        
        dataset = input_format.create_dataset(file_content)
        
        # بررسی وجود Poll
        try:
            poll = Poll.objects.get(id=poll_id)
        except Poll.DoesNotExist:
            raise ValueError(f"Poll with id {poll_id} does not exist")
        
        # ایجاد resource instance و import data
        resource = QuestionResource()
        
        # شروع import process
        result = resource.import_data(
            dataset, 
            dry_run=False,
            raise_errors=True,
            poll=poll_id
        )
        
        # بازگرداندن نتیجه
        print(f'--- Question Import Task Completed ----<----')
        return {
            'success': True,
            'message': f'Successfully imported {len(dataset)} records',
            'total_new': result.totals.get('new', 0),
            'total_update': result.totals.get('update', 0),
            'total_delete': result.totals.get('delete', 0),
            'total_skip': result.totals.get('skip', 0),
            'has_errors': result.has_errors(),
            'errors': [str(error.error) for error in result.base_errors] if result.has_errors() else []
        }
        
    except Exception as e:
        # ثبت خطا و بازگرداندن
        return {
            'success': False,
            'message': str(e),
            'error_type': type(e).__name__
        }


@shared_task
def cleanup_temp_question_import_files():
    """
    تسک برای پاک کردن فایل‌های موقت import سوالات که قدیمی شده‌اند
    """
    import datetime
    from django.utils import timezone
    
    # پاک کردن فایل‌هایی که بیش از 24 ساعت قدیمت دارند
    cutoff_time = timezone.now() - datetime.timedelta(hours=24)
    
    # این بخش نیاز به پیاده سازی logic پاک کردن فایل های قدیمی دارد
    # فعلاً فقط یک placeholder است
    return "Question import cleanup completed"