from django.shortcuts import render
from django.core.exceptions import PermissionDenied
from rest_framework.response import Response as JsonResponse
from rest_framework import status, views, generics
from apps.evaluation.models import *
from apps.employee.models import *
from apps.evaluation.serializers import *
from rest_framework.exceptions import NotFound
from django.db import transaction
import jdatetime
from datetime import datetime

class PollInfoAPIView(generics.RetrieveAPIView):
    serializer_class = PollInfoSerializer
    lookup_field = 'access_token'

    def get_object(self):
        access_token = self.kwargs.get('access_token')
        try:
            return PollAssignment.objects.get(access_token=access_token)
        except PollAssignment.DoesNotExist:
            return None

    def get(self, request, *args, **kwargs):
        instance = self.get_object()
        if not instance:
            return JsonResponse({"error": "Poll assignment not found."}, status=status.HTTP_404_NOT_FOUND)
        serializer = self.get_serializer(instance)
        return JsonResponse(serializer.data)
    
            
        

class QuestionDetailAPIView(generics.RetrieveAPIView):
    serializer_class = QuestionDetailSerializer
    lookup_field = 'id'  # این فیلد برای جستجوی سوال (Question) استفاده می‌شود
    queryset = Question.objects.all()  # کوئری‌ست پیش‌فرض برای سوالات

    def get_poll_assignment(self):
        access_token = self.kwargs.get('access_token')
        try:
            return PollAssignment.objects.filter(access_token=access_token).last()
        except PollAssignment.DoesNotExist:
            raise JsonResponse(
                {"error": "Poll assignment not found."},
                status=status.HTTP_404_NOT_FOUND
            )

    def get_serializer_context(self):
        context = super().get_serializer_context()
        poll_assignment = self.get_poll_assignment()
        context['evaluator'] = poll_assignment.assigned_to  
        context['poll_assignment'] = poll_assignment          
        return context

    def get(self, request, *args, **kwargs):
        try:
            self.get_poll_assignment()  # بررسی وجود PollAssignment            
            question = self.get_object()  # دریافت سوال از طریق id
        except Exception:
            return JsonResponse(
                {"error": "Question not found."},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # سریالایز کردن سوال و برگرداندن پاسخ
        serializer = self.get_serializer(question)
        return JsonResponse(serializer.data)


class SubmitResponsesAPIView(generics.CreateAPIView):
    serializer_class = SubmitResponsesSerializer

    def create(self, request, *args, **kwargs):
        # Initialize the serializer and validate data
        print(f'---> {request.data}')
        serializer = self.get_serializer(data=request.data)
        if not serializer.is_valid():
            return JsonResponse(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        validated_data = serializer.validated_data

        try:
            # Extract validated data
            poll_assignment = serializer.poll_assignment
            question = serializer.question
            selected_employees_data = validated_data['selected_employees']
            evaluator = poll_assignment.assigned_to

            # Create or update responses
            with transaction.atomic():  # Ensure all operations are atomic
                for employee_data in selected_employees_data:
                    selected_employee_id = employee_data['id']
                    rating = employee_data['rating']
                    comment = employee_data.get('comment', None)

                    # Get the selected employee
                    selected_employee = Employee.objects.get(id=selected_employee_id)

                    # Create or update the response
                    Response.objects.update_or_create(
                        poll_assignment=poll_assignment,
                        question=question,
                        selected_employee=selected_employee,
                        defaults={
                            'evaluator': evaluator,
                            'rating': rating,
                            'comment': comment
                        }
                    )

                # Check if Poll's start_date is not set
                poll = poll_assignment.poll
                if not poll.start_date:
                    # Set start_date to the current date (Jalali)
                    jalali_date = jdatetime.datetime.now()
                    poll.start_date = jalali_date.togregorian()  # Convert Jalali to Gregorian
                    poll.save()


        except Exception as e:
            print(f'-view-error--> {e}')
            return JsonResponse({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

        # Return success response
        return JsonResponse({"message": "Responses submitted successfully."}, status=status.HTTP_201_CREATED)