import pandas as pd

# تعریف داده‌های نمونه به صورت دستی
data = {
    'national_code': [
        '0011223344', '0011223355', '0011223366', '0011223377', '0011223388',
        '0011223399', '0011223400', '0011223411', '0011223422', '0011223433',
        '0011223444', '0011223455', '0011223466', '0011223477', '0011223488',
        '0011223499', '0011223500', '0011223511', '0011223522', '0011223533'
    ],
    'fullname': [
        'علی احمدی', 'محسن رضایی', 'مصطفی محمدی', 'سعید حسینی', 'حسین طاهری',
        'رضا عباسی', 'امیرحسین خواجه‌پور', 'مصطفی ابراهیمی', 'احمد رحیمی', 'مصطفی علیزاده',
        'سجاد موسوی', 'بهزاد کاظمی', 'مصطفی اسماعیلی', 'مصطفی حسینی', 'مصطفی رحمانی',
        'مصطفی علیپور', 'مصطفی علیمحمدی', 'مصطفی علیجانی', 'مصطفی علیخانی', 'مصطفی علیشاهی'
    ],
    'company': [
        'شرکت توسعه نرم‌افزار ایران', 'شرکت توسعه نرم‌افزار ایران', 'شرکت توسعه نرم‌افزار ایران',
        'شرکت هوش مصنوعی ایران', 'شرکت هوش مصنوعی ایران', 'شرکت هوش مصنوعی ایران',
        'شرکت امنیت اطلاعات ایران', 'شرکت امنیت اطلاعات ایران', 'شرکت امنیت اطلاعات ایران',
        'شرکت توسعه نرم‌افزار ایران', 'شرکت توسعه نرم‌افزار ایران', 'شرکت توسعه نرم‌افزار ایران',
        'شرکت هوش مصنوعی ایران', 'شرکت هوش مصنوعی ایران', 'شرکت هوش مصنوعی ایران',
        'شرکت امنیت اطلاعات ایران', 'شرکت امنیت اطلاعات ایران', 'شرکت امنیت اطلاعات ایران',
        'شرکت توسعه نرم‌افزار ایران', 'شرکت هوش مصنوعی ایران'
    ],
    'position': [
        'توسعه‌دهنده وب (Frontend)', 'توسعه‌دهنده وب (Backend)', 'مهندس داده', 'برنامه‌نویس هوش مصنوعی',
        'مهندس یادگیری ماشین', 'متخصص پردازش تصویر', 'مهندس امنیت شبکه', 'متخصص امنیت سیستم',
        'برنامه‌نویس امنیت اطلاعات', 'توسعه‌دهنده اپلیکیشن موبایل', 'برنامه‌نویس بازی', 'مهندس سخت‌افزار امنیت',
        'برنامه‌نویس هوش مصنوعی', 'مهندس داده', 'برنامه‌نویس یادگیری عمیق', 'مهندس امنیت ابری',
        'برنامه‌نویس امنیت وب', 'برنامه‌نویس امنیت دیتابیس', 'برنامه‌نویس DevOps', 'مهندس رباتیک نرم‌افزاری'
    ]
}

# تبدیل داده‌ها به DataFrame
df = pd.DataFrame(data)

# حذف رکوردهای تکراری بر اساس ستون‌های 'fullname' و 'national_code'
df_unique = df.drop_duplicates(subset=['fullname', 'national_code'])

# ذخیره DataFrame یونیک شده در فایل اکسل
df_unique.to_excel('employees1.xlsx', index=False, encoding='utf-8')

print("فایل اکسل یونیک با موفقیت ایجاد شد.")
