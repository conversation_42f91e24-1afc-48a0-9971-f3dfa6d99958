import json

from django import forms

from limitless_dashboard.fields.tinyeditor import TinyWidget
# from limitless_dashboard.fields.summernote import 

from dynamic_preferences.preferences import Section
from dynamic_preferences.registries import global_preferences_registry
from dynamic_preferences.types import BasePreferenceType, BaseSerializer, LongStringPreference, StringPreference, \
    FilePreference
from utils.json_editor_field import JsonEditorWidget


class EditorPreferences(LongStringPreference):
    widget = TinyWidget(attrs={'class': 'editor-field'})


@global_preferences_registry.register
class AboutUsConfig(EditorPreferences):
    section = Section('aboutus', verbose_name='AboutUsConfig')
    name = 'aboutus'
    required = False
    verbose_name = 'About Us'
    default = ''


class JsonSerializer(BaseSerializer):

    @classmethod
    def serialize(cls, value, **kwargs):
        return json.dumps(value, ensure_ascii=False)
    
    @classmethod
    def to_python(cls, value, **kwargs):
        if isinstance(value, str) and len(value.strip()) > 0:
            try:
                return json.loads(value)
            except json.JSONDecodeError as e:
                try:
                    value_replaced = value.replace("'", '"')
                    return json.loads(value_replaced)
                except json.JSONDecodeError as e2:
                    return {}
        return value
    


get_fqa_courses_schema = {
        'type': "array",
        'format': 'table',
        'title': ' ',
        'items': {
            'type': 'object',
            'title': str('Questions about courses'),
            'properties': {
                'question': {'type': 'string', "format": "textarea", 'title': str('Question')},
                'answer': {
                    'type': "string",
                    "format": "textarea",
                    'title': str('Answer')
                }
            }
        }
    }


class JsonFieldFAQCourse(BasePreferenceType):
    field_class = forms.JSONField
    serializer = JsonSerializer
    widget = JsonEditorWidget(attrs={'schema': get_fqa_courses_schema})


@global_preferences_registry.register
class FAQCourseConfig(JsonFieldFAQCourse):
    widget = JsonEditorWidget(attrs={'schema': get_fqa_courses_schema}) 
    section = Section('FAQ_Course', verbose_name='Questions about courses')
    name = 'FAQ_Course'
    required = False
    verbose_name = 'FAQ Course'
    default = {}



get_fqa_general_schema = {
        'type': "array",
        'format': 'table',
        'title': ' ',
        'items': {
            'type': 'object',
            'title': str('Questions General'),
            'properties': {
                'question': {'type': 'string', "format": "textarea", 'title': str('Question')},
                'answer': {
                    'type': "string",
                    "format": "textarea",
                    'title': str('Answer')
                }
            }
        }
    }

class JsonFieldFAQGeneral(BasePreferenceType):
    field_class = forms.JSONField
    serializer = JsonSerializer
    widget = JsonEditorWidget(attrs={'schema': get_fqa_general_schema})



@global_preferences_registry.register
class FAQGeneralConfig(JsonFieldFAQGeneral):
    widget = JsonEditorWidget(attrs={'schema': get_fqa_general_schema}) 
    section = Section('FAQ_General', verbose_name='Questions General')
    name = 'FAQ_General'
    required = False
    verbose_name = 'FAQ General'
    default = {}





support_fields = {
    "type": "object",
    "format": "table",
    "title": "",
    "required_by_default": 1,
    "required": ['telegram_number', "whatsapp_number"],
    "properties": {
        "telegram_number": {"type": "string", "title": "Telegram Number"},        
        "whatsapp_number": {"type": "string", "title": "Whatsapp Number"},
    }
}



class JsonFieldSupport(BasePreferenceType):
    field_class = forms.JSONField
    serializer = JsonSerializer
    widget = JsonEditorWidget(attrs={'schema': support_fields})

@global_preferences_registry.register
class SupportConfig(JsonFieldSupport):
    section = Section('support', verbose_name='Support Detail')
    name = 'support'
    required = False
    verbose_name = 'Support Detail'
    default = {}






card_fields = {
    "type": "object",
    "format": "table",
    "title": "",
    "required_by_default": 1,
    "required": ['card_number', 'card_name',"whatsapp_number"],
    "properties": {
        "card_number": {"type": "string", "title": "Card Number"},        
        "card_name": {"type": "string", "title": "Card Name"},        
        "whatsapp_number": {"type": "string", "title": "Whatsapp Number"},
    }
}



class JsonFieldCard(BasePreferenceType):
    field_class = forms.JSONField
    serializer = JsonSerializer
    widget = JsonEditorWidget(attrs={'schema': card_fields})

@global_preferences_registry.register
class SupportConfig(JsonFieldCard):
    section = Section('card', verbose_name='Card Detail')
    name = 'card'
    required = False
    verbose_name = 'Card Detail'
    default = {}

