{% extends "dynamic_preferences/base.html" %}
{% load i18n %}
{% block content %}

    {# we continue to pass the sections key in case someone subclassed the template and use these #}
    {% include "dynamic_preferences/sections.html" with registry=registry sections=registry.sections %}

    <form action="" enctype="multipart/form-data" method="post">
        {% csrf_token %}
        {{ form.as_p }}
        <input type="submit" value="{% trans 'Submit' %}" />
    </form>
{% endblock %}
