# Generated by Django 3.1 on 2020-08-21 08:37

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("dynamic_preferences_users", "0001_initial"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="userpreferencemodel",
            name="name",
            field=models.Char<PERSON>ield(db_index=True, max_length=150, verbose_name="Name"),
        ),
        migrations.AlterField(
            model_name="userpreferencemodel",
            name="raw_value",
            field=models.TextField(blank=True, null=True, verbose_name="Raw Value"),
        ),
        migrations.AlterField(
            model_name="userpreferencemodel",
            name="section",
            field=models.CharField(
                blank=True,
                db_index=True,
                default=None,
                max_length=150,
                null=True,
                verbose_name="Section Name",
            ),
        ),
    ]
