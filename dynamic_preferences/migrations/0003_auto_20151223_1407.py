# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from django.db import models, migrations


class Migration(migrations.Migration):

    dependencies = [
        ("dynamic_preferences", "0002_auto_20150712_0332"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="globalpreferencemodel",
            name="name",
            field=models.CharField(max_length=150, db_index=True),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="globalpreferencemodel",
            name="section",
            field=models.CharField(
                max_length=150,
                null=True,
                default=None,
                db_index=True,
                blank=True,
                verbose_name="Section Name",
            ),
            preserve_default=True,
        ),
    ]
