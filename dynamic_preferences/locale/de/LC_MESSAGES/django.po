# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-04-15 13:48+0200\n"
"PO-Revision-Date: 2018-11-09 17:14+0100\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"X-Generator: Poedit 2.1.1\n"

#: .\admin.py:56
msgid "Default Value"
msgstr "Standardwert"

#: .\admin.py:65 .\models.py:22
msgid "Section Name"
msgstr "Abschnitt"

#: .\apps.py:9
msgid "Dynamic Preferences"
msgstr "Dynamische Einstellungen"

#: .\models.py:25
msgid "Name"
msgstr "Name"

#: .\models.py:28
msgid "Raw Value"
msgstr "Wert"

#: .\models.py:42
msgid "Verbose Name"
msgstr "Bezeichnung"

#: .\models.py:47
msgid "Help Text"
msgstr "Hilfetext"

#: .\models.py:84
msgid "Global preference"
msgstr "Globale Einstellung"

#: .\models.py:85
msgid "Global preferences"
msgstr "Globale Einstellungen"

#: .\templates\dynamic_preferences\form.html:11
msgid "Submit"
msgstr "Absenden"

#: .\users\apps.py:11
msgid "Preferences - Users"
msgstr "Einstellungen - Benutzer"

#: .\users\models.py:15
msgid "user preference"
msgstr "Benutzer Einstellung"

#: .\users\models.py:16
msgid "user preferences"
msgstr "Benutzer Einstellungen"
