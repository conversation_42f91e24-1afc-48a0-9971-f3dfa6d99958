# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-02-16 15:12+0330\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#: admin.py:69
msgid "Default Value"
msgstr "مقدار پیشفرض"

#: admin.py:78 models.py:30
msgid "Section Name"
msgstr "عنوان بخش"

#: apps.py:10
msgid "Dynamic Preferences"
msgstr "تنظیمات"

#: models.py:34
msgid "Name"
msgstr "نام"

#: models.py:37
msgid "Raw Value"
msgstr "مقدار"

#: models.py:51
msgid "Verbose Name"
msgstr "نام"

#: models.py:57
msgid "Help Text"
msgstr "متن راهنما"

#: models.py:94
msgid "Global preference"
msgstr "تنطیمات عمومی"

#: models.py:95
msgid "Global preferences"
msgstr "تنطیمات عمومی"

#: templates/dynamic_preferences/form.html:11
msgid "Submit"
msgstr "ثبت"

#: users/apps.py:11
msgid "Preferences - Users"
msgstr ""

#: users/models.py:14
msgid "user preference"
msgstr ""

#: users/models.py:15
msgid "user preferences"
msgstr ""
