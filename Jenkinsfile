pipeline {
    environment {
        develop_server_ip = ''
        develop_server_name = ''
        production_server_ip = "*************"
        production_server_name = "newhorizon_germany_001_server"
        project_path = "/projects/imam-javad/imam-javad_backend"
        version = "master"
		gitBranch = "origin/master"
    }
    agent any
    stages {
        stage('deploy'){
            steps{
                script{
                    if(gitBranch=="origin/master"){
                        withCredentials([usernamePassword(credentialsId: production_server_name, usernameVariable: 'USERNAME', passwordVariable: 'PASSWORD')]) {
                            sh 'sshpass -p $PASSWORD ssh -p 1782 $USERNAME@$production_server_ip   -o StrictHostKeyChecking=no "cd $project_path && ./runner.sh"'

                            def lastCommit = sh(script: 'git log -1 --pretty=format:"%h - %s (%an)"', returnStdout: true).trim()
                            sh """
                                curl -F chat_id=1457670318 \
                                     -F message_thread_id=6 \
                                     -F document=@/var/jenkins_home/jobs/${env.JOB_NAME}/builds/${env.BUILD_NUMBER}/log \
                                     -F caption='Project name: #${env.JOB_NAME} \nBuild status is ${currentBuild.currentResult} \nBuild url: ${BUILD_URL} \nLast Commit: ${lastCommit}' \
                                     https://api.telegram.org/bot7207581748:AAFeymryw7S44D86LYfWqYK-tSNeV3TOwBs/sendDocument
                            """
                        }
                    }
                }
            }
        }
    }
}

