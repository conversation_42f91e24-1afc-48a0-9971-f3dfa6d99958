{% load i18n %}
<textarea style="display: none" name="{{ widget.name }}"
        {% include "django/forms/widgets/attrs.html" %}
>{% if widget.value %}{{ widget.value }}{% endif %}</textarea>
<div class="json-view-editor" id='date-view-editor-{{ widget.attrs.id }}'></div>


<script defer="defer">
    $(document).ready(function () {
        function init_editor_json_editor() {
            let editor_ = document.getElementById("{{ widget.attrs.id }}")
            let schema_str = JSON.parse(editor_.value)
            let json_viewer_div = $('#date-view-editor-{{ widget.attrs.id }}')

            let jsoneditor__ = new JSONEditor(
                json_viewer_div[0], {
                    theme: 'bootstrap4',
                    schema: {{ widget.attrs.schema|safe }},
                    disable_edit_json: true,
                    disable_properties: true,
                    disable_array_delete_all_rows: true,
                    disable_array_delete_last_row: true,
                    disable_array_reorder: true,
                    grid_columns: 3,
                    prompt_before_delete: false,
                    disable_collapse: true,
                    startval: schema_str || []
                })
            editor_.editor = jsoneditor__
            jsoneditor__.on('change', () => {
                $(editor_).val(JSON.stringify(jsoneditor__.getValue()))
            })
        }

        init_editor_json_editor()
    })
</script>

