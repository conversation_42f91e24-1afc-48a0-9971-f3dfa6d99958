{% extends 'admin/base_site.html' %}
{% load static %}
{% block content %}
    {{ block.super }}
    <div class="p-3">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Monthly User Chart</h5>
                    </div>

                    <div class="card-body">
                        <div class="chart-container">
                            <div class="chart has-fixed-height" id="area_zoom"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block scripts %}
    {% if request.user.is_superuser %}
        <script src="{% static "echarts.js" %}"></script>

        <script>
            function init_users_chart(legends, series, dates, selected_legend) {
                var area_zoom_element = document.getElementById('area_zoom');

                if (area_zoom_element) {

                    // Initialize chart
                    var area_zoom = echarts.init(area_zoom_element, null, {renderer: 'svg'});

                    //
                    // Chart config
                    //

                    // Options
                    area_zoom.setOption({

                        // Define colors
                        color: ['#b6a2de', '#26A69A', '#ffb980', '#d87a80'],

                        // Global text styles
                        textStyle: {
                            fontFamily: 'var(--body-font-family)',
                            color: 'var(--body-color)',
                            fontSize: 14,
                            lineHeight: 22,
                            textBorderColor: 'transparent'
                        },

                        // Chart animation duration
                        animationDuration: 750,

                        // Setup grid
                        grid: {
                            left: 0,
                            right: 40,
                            top: 35,
                            bottom: 60,
                            containLabel: true
                        },

                        // Add legend
                        legend: {
                            data: legends,
                            itemHeight: 8,
                            itemGap: 30,
                            textStyle: {
                                color: 'var(--body-color)'
                            },
                            selected: selected_legend,
                        },

                        // Add tooltip
                        tooltip: {
                            trigger: 'axis',
                            className: 'shadow-sm rounded',
                            backgroundColor: 'var(--white)',
                            borderColor: 'var(--gray-400)',
                            padding: 15,
                            textStyle: {
                                color: '#000'
                            }
                        },

                        // Horizontal axis
                        xAxis: [{
                            type: 'category',
                            boundaryGap: false,
                            axisLabel: {
                                color: 'rgba(var(--body-color-rgb), .65)'
                            },
                            axisLine: {
                                lineStyle: {
                                    color: 'var(--gray-500)'
                                }
                            },
                            splitLine: {
                                show: true,
                                lineStyle: {
                                    color: 'var(--gray-300)'
                                }
                            },
                            data: dates
                        }],

                        // Vertical axis
                        yAxis: [{
                            type: 'value',
                            axisLabel: {
                                formatter: '{value} ',
                                color: 'rgba(var(--body-color-rgb), .65)'
                            },
                            axisLine: {
                                show: true,
                                lineStyle: {
                                    color: 'var(--gray-500)'
                                }
                            },
                            splitLine: {
                                lineStyle: {
                                    color: 'var(--gray-300)'
                                }
                            },
                            splitArea: {
                                show: true,
                                areaStyle: {
                                    color: ['rgba(var(--white-rgb), .01)', 'rgba(var(--black-rgb), .01)']
                                }
                            }
                        }],

                        // Axis pointer
                        axisPointer: [{
                            lineStyle: {
                                color: 'var(--gray-600)'
                            }
                        }],

                        // Zoom control
                        dataZoom: [
                            {
                                type: 'inside',
                                start: 0,
                                end: 100
                            },
                            {
                                show: true,
                                type: 'slider',
                                start: 0,
                                end: 100,
                                height: 40,
                                bottom: 10,
                                borderColor: 'var(--gray-400)',
                                fillerColor: 'rgba(0,0,0,0.05)',
                                textStyle: {
                                    color: 'var(--body-color)'
                                },
                                handleStyle: {
                                    color: '#8fb0f7',
                                    borderColor: 'rgba(0,0,0,0.25)'
                                },
                                moveHandleStyle: {
                                    color: '#8fb0f7',
                                    borderColor: 'rgba(0,0,0,0.25)'
                                },
                                dataBackground: {
                                    lineStyle: {
                                        color: 'var(--gray-500)'
                                    },
                                    areaStyle: {
                                        color: 'var(--gray-500)',
                                        opacity: 0.1
                                    }
                                }
                            }
                        ],

                        // Add series
                        series: series

                    });
                }

            }
        </script>
        <script>


            $.ajax({
                url: '/dashboard-charts/',
                success: (
                    {complete_users_chart}
                ) => {
                    init_users_chart(complete_users_chart.legend, complete_users_chart.series, complete_users_chart.dates, complete_users_chart.selected_legend)
                    console.log(complete_users_chart)
                },
                error: (err) => {
                    console.log(err)
                }

            })


        </script>

    {% endif %}
{% endblock %}