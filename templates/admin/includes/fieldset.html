<div class="card ">
    <div class="{% if fieldset.classes %}
card-group-control card-group-control-left
{% endif %}" id="accordion-default-{{ fieldset.name | cut:" " }}">
        {% if inline_admin_formset.opts.verbose_name_plural %}
            {#            <div class="card-header bg-light header-elements-inline">#}
            {#                <h6 class="card-title">#}
            {#                    {{ inline_admin_formset.opts.verbose_name_plural|capfirst }}#}
            {#                </h6>#}
            {#            </div>#}
        {% else %}
            {% if fieldset.name %}
                <div class="card-header bg-light header-elements-inline">
                    <h6 class="card-title">
                        <a class="text-default" data-toggle="collapse"
                           href="#{{ fieldset.name | cut:" " }}">
                            {{ fieldset.name }}
                        </a>
                    </h6>
                </div>
            {% endif %}
        {% endif %}
        <div class="card-body">
            <fieldset class="module aligned {{ fieldset.classes }} collapsed" id="{{ fieldset.name | cut:" " }}"
                      data-parent="#accordion-default-{{ fieldset.name | cut:" " }}">
                {% if fieldset.description %}
                    <div class="description">{{ fieldset.description|safe }}</div>
                {% endif %}
                {% for line in fieldset %}
                    <div class="col-md-12 {% if not line.fields|length_is:'1' %} row inline-fields {% endif %} {% if line.fields|length_is:'1' and line.errors %} errors{% endif %}{% if not line.has_visible_field %} hidden{% endif %}{% for field in line %}{% if field.field.name %} field-{{ field.field.name }}{% endif %}{% endfor %}">
                        {% for field in line %}
                            <div {% if not line.fields|length_is:'1' %}
                                class="col-md my-2 fieldBox {% if field.field.name %}field-{{ field.field.name }}{% endif %}
                                        {% if not field.is_readonly and field.errors %} errors{% endif %}
                                        {% if field.field.is_hidden %} hidden{% endif %}"
                            {% else %}
                                class="form-group col-md-12"
                            {% endif %}
                                    {% if field.is_checkbox %}
                                class="checkbox-row"
                                    {% endif %}>
                                {% if line.fields|length_is:'1' %}
                                    <div class="row">
                                        {% if not line.fields|length_is:'1' and not field.is_readonly %}
                                            {{ field.errors }}{% endif %}
                                        <span class="col-form-label {% if field.field.widget_type %}col-lg-2{% else %}col-lg-10{% endif %} ">{{ field.label_tag }}</span>
                                        {% if field.is_readonly %}
                                            <div class="mt-2 border-bottom col-lg-12 ">{{ field.contents }}</div>
                                        {% else %}
                                            <div class="{% if field.field.widget_type %}col-lg-12{% else %}col-lg-12{% endif %} pl-0">
                                                {{ field.field }}
                                                {% if line.errors %}
                                                    <label id="with_icon-error" class="validation-invalid-label"
                                                           for="with_icon">
                                                        {{ line.errors }}
                                                    </label>
                                                {% endif %}
                                                {% if field.field.help_text %}
                                                    <span class="form-text text-muted">{{ field.field.help_text|safe }}</span>
                                                {% endif %}
                                            </div>
                                        {% endif %}
                                    </div>
                                {% else %}
                                    {% if not line.fields|length_is:'1' and not field.is_readonly %}
                                        {{ field.errors }}{% endif %}
                                    <span class="col-form-label {% if field.field.widget_type %}col-lg-2{% else %}col-lg-10{% endif %} ">{{ field.label_tag }}</span>
                                    {% if field.is_readonly %}
                                        <div class="mt-2 border-bottom col-lg-12 ">{{ field.contents }}</div>
                                    {% else %}
                                        <div class="{% if field.field.widget_type %}col-lg-12{% else %}col-lg-12{% endif %} pl-0">
                                            {{ field.field }}
                                            {% if line.errors %}
                                                <label id="with_icon-error" class="validation-invalid-label"
                                                       for="with_icon">
                                                    {{ line.errors }}
                                                </label>
                                            {% endif %}
                                            {% if field.field.help_text %}
                                                <span class="form-text text-muted">{{ field.field.help_text|safe }}</span>
                                            {% endif %}
                                        </div>
                                    {% endif %}
                                {% endif %}


                            </div>
                        {% endfor %}
                    </div>
                {% endfor %}
            </fieldset>

        </div>
    </div>
</div>
