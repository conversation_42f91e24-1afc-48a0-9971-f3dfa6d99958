<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Search Names</title>
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <style>
        body {
            display: flex;
            justify-content: center;
            height: 100vh;
        }

        .search-container {
            text-align: center;
        }

        #results p {

        }
    </style>
</head>
<body>

<div class="container">
    <div class="row">
        <div class="col-md-6 offset-md-3 search-container" style="margin-top: 15vh">
            <h2 class="mb-4">Search Names</h2>
            <input type="text" class="form-control mb-3" id="searchInput" placeholder="Enter a name to search">
            <button class="btn btn-primary" id="searchBtn">Search</button>
            <div id="results-wrapper" class="mt-3 d-none">
                <div class="loading">loading...</div>
                <hr>
                <div id="results">
                    By Meaning:
                    <div id="meaning" style="background-color: rgba(54,144,106,0.62)"></div>
                    <hr>

                    By Name:
                    <div id="voice" style="background-color: rgba(141,73,73,0.69)"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bootstrap JS and AJAX request -->
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
<script>
    $(document).ready(function () {
        $('#searchBtn').click(function () {
            $("#results-wrapper").toggleClass('d-none')
            $('.loading').removeClass('d-none')
            var searchValue = $('#searchInput').val();
            $.ajax({
                type: 'POST',
                url: '',
                data: {name: searchValue},
                success: function (response) {
                    $('.loading').addClass('d-none')
                    window.x = response
                    $("#results-wrapper").removeClass('d-none')
                    $("#meaning").html('')
                    $("#voice").html('')

                    $(response.similar_meaning).each(function (index, item) {
                        $("#results #meaning").append(
                            `<p>${item}</p>`
                        )
                    })

                    $(response.similar_dictation).each(function (index, item) {
                        $("#results #voice").append(
                            `<p>${item}</p>`
                        )
                    })

                },
                error: function (xhr, status, error) {
                    console.error(error);
                    $('#results').text('Error occurred while fetching results.');
                }
            });
        });
    });
</script>

</body>
</html>
